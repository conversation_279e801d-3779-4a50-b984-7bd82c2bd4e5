{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .review-dashboard {
        padding: 20px;
    }
    .filters-section {
        background: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .filters-row {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    .filter-group label {
        font-size: 12px;
        font-weight: bold;
        color: #333;
    }
    .filter-group select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 150px;
    }
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    .stat-card {
        background: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        text-align: center;
    }
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #dc3545;
    }
    .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    .review-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .review-table th,
    .review-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    .review-table th {
        background: #f8f9fa;
        font-weight: 600;
    }
    .review-table tr:hover {
        background: #fff3cd;
    }
    .error-preview {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #666;
    }
    .failure-reason-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: bold;
    }
    .reason-data { background: #d4edda; color: #155724; }
    .reason-bot { background: #d1ecf1; color: #0c5460; }
    .reason-website { background: #fff3cd; color: #856404; }
    .reason-system { background: #f8d7da; color: #721c24; }
    .reason-unknown { background: #e2e3e5; color: #383d41; }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .btn-sm {
        padding: 4px 8px;
        font-size: 11px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    .btn-primary { background: #007cba; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: #212529; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-sm:hover { opacity: 0.8; }
    .reviewed-indicator {
        font-size: 12px;
        color: #28a745;
        font-weight: bold;
    }
    .filter-btn {
        padding: 8px 16px;
        background: #007cba;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .filter-btn:hover {
        background: #0056b3;
    }
</style>
{% endblock %}

{% block content %}
<div class="review-dashboard">
    <h1>🔍 Review Queue Dashboard</h1>
    <p style="color: #666; margin-bottom: 20px;">Failed jobs across all locations requiring admin review</p>
    
    <!-- Statistics -->
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ total_review_jobs }}</div>
            <div class="stat-label">Total in Review</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #28a745;">{{ reviewed_jobs }}</div>
            <div class="stat-label">Already Reviewed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #ffc107;">{{ total_review_jobs|add:"-"|add:reviewed_jobs }}</div>
            <div class="stat-label">Needs Review</div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="filters-section">
        <form method="get" style="margin: 0;">
            <div class="filters-row">
                <div class="filter-group">
                    <label>Location:</label>
                    <select name="location">
                        <option value="">All Locations</option>
                        {% for location in locations %}
                        <option value="{{ location.id }}" {% if location.id|stringformat:"s" == selected_location %}selected{% endif %}>
                            {{ location.location_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>Error Type:</label>
                    <select name="error_type">
                        <option value="">All Error Types</option>
                        {% for error_type in error_types %}
                        <option value="{{ error_type }}" {% if error_type == selected_error_type %}selected{% endif %}>
                            {{ error_type|title }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="submit" class="filter-btn">Apply Filters</button>
                
                {% if selected_location or selected_error_type %}
                <a href="{% url 'queue_system:review_queue_dashboard' %}" class="filter-btn" style="background: #6c757d;">Clear Filters</a>
                {% endif %}
            </div>
        </form>
    </div>
    
    <!-- Review Jobs Table -->
    {% if review_jobs %}
    <table class="review-table">
        <thead>
            <tr>
                <th>Job ID</th>
                <th>Customer</th>
                <th>Location</th>
                <th>Failure Reason</th>
                <th>Error Details</th>
                <th>Retries</th>
                <th>Created</th>
                <th>Review Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for job in review_jobs %}
            <tr>
                <td>
                    <a href="{% url 'queue_system:job_details' job.id %}" style="color: #007cba; text-decoration: none;">
                        #{{ job.id }}
                    </a>
                </td>
                <td>{{ job.order.first_name }} {{ job.order.surname }}</td>
                <td>{{ job.location.location_name }}</td>
                <td>
                    {% if job.failure_reason %}
                        <span class="failure-reason-badge 
                            {% if 'data' in job.failure_reason %}reason-data
                            {% elif 'bot' in job.failure_reason %}reason-bot
                            {% elif 'website' in job.failure_reason or 'authentication' in job.failure_reason %}reason-website
                            {% elif 'network' in job.failure_reason or 'unknown' in job.failure_reason %}reason-system
                            {% else %}reason-unknown{% endif %}">
                            {{ job.failure_reason|title }}
                        </span>
                    {% else %}
                        <span class="failure-reason-badge reason-unknown">Unknown</span>
                    {% endif %}
                </td>
                <td>
                    {% if job.error_message %}
                        <div class="error-preview" title="{{ job.error_message }}">
                            {{ job.error_message|truncatechars:50 }}
                        </div>
                    {% else %}
                        <span style="color: #999;">No error message</span>
                    {% endif %}
                </td>
                <td>{{ job.retry_count }}/{{ job.max_retries }}</td>
                <td>{{ job.created_at|date:"M d, H:i" }}</td>
                <td>
                    {% if job.reviewed_by %}
                        <div class="reviewed-indicator">
                            ✓ {{ job.reviewed_by }}
                            {% if job.reviewed_at %}
                                <br><small>{{ job.reviewed_at|date:"M d, H:i" }}</small>
                            {% endif %}
                        </div>
                    {% else %}
                        <span style="color: #dc3545; font-size: 12px;">⚠ Needs Review</span>
                    {% endif %}
                </td>
                <td>
                    <div class="action-buttons">
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-sm btn-primary">View</a>

                        {% if job.status == 'failed' or job.status == 'review' %}
                            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="requeue">
                                <input type="hidden" name="reason" value="Requeued from review dashboard">
                                <button type="submit" class="btn-sm btn-success" onclick="return confirm('Requeue this job for processing?')">
                                    🔄 Requeue
                                </button>
                            </form>

                            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="prioritize">
                                <button type="submit" class="btn-sm btn-warning" onclick="return confirm('Mark as priority?')">
                                    ⚡ Priority
                                </button>
                            </form>
                        {% endif %}

                        {% if job.status != 'completed' %}
                            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="cancel">
                                <button type="submit" class="btn-sm btn-danger" onclick="return confirm('Cancel this job permanently?')">
                                    ❌ Cancel
                                </button>
                            </form>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div style="text-align: center; padding: 60px; background: white; border-radius: 8px; border: 1px solid #ddd;">
        <h3 style="color: #28a745;">✅ No Jobs in Review Queue</h3>
        <p style="color: #666;">All jobs are processing normally or have been resolved.</p>
        {% if selected_location or selected_error_type %}
        <p><a href="{% url 'queue_system:review_queue_dashboard' %}">Clear filters</a> to see all review jobs.</p>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Navigation -->
    <div style="text-align: center; margin-top: 30px;">
        <a href="{% url 'queue_system:queue_overview' %}" style="color: #007cba; text-decoration: none; margin-right: 20px;">
            ← Back to Overview
        </a>
        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" style="color: #007cba; text-decoration: none;">
            All Jobs
        </a>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
