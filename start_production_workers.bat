@echo off
echo 🚀 STARTING PRODUCTION WORKERS
echo ===============================
echo This will start Celery workers for all location queues
echo Workers run in background (detached mode)
echo ===============================

python manage.py start_production_workers --workers-per-location=1 --concurrency=1 --beat

echo.
echo ✅ Production workers started
echo 🔍 Check status: http://localhost:8000/queue/admin/queue-overview/
pause
