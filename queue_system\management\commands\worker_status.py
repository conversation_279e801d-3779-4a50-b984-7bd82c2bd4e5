from django.core.management.base import BaseCommand
from queue_system.worker_manager import get_worker_status, start_location_workers, stop_location_workers

class Command(BaseCommand):
    help = 'Check status of auto-started workers'

    def add_arguments(self, parser):
        parser.add_argument('--start', action='store_true', help='Start workers manually')
        parser.add_argument('--stop', action='store_true', help='Stop workers manually')
        parser.add_argument('--restart', action='store_true', help='Restart workers')

    def handle(self, *args, **options):
        if options['start']:
            self.stdout.write("🚀 Starting location workers...")
            start_location_workers()
            return
        
        if options['stop']:
            self.stdout.write("🛑 Stopping location workers...")
            stop_location_workers()
            return
        
        if options['restart']:
            self.stdout.write("🔄 Restarting location workers...")
            stop_location_workers()
            import time
            time.sleep(2)
            start_location_workers()
            return
        
        # Show status
        status = get_worker_status()
        
        self.stdout.write(self.style.SUCCESS("📊 AUTO-STARTED WORKER STATUS"))
        self.stdout.write("=" * 50)
        
        if not status['workers_started']:
            self.stdout.write("❌ No auto-started workers found")
            self.stdout.write("💡 Workers auto-start when you run: python manage.py runserver")
            return
        
        self.stdout.write(f"✅ Workers Started: {status['workers_started']}")
        self.stdout.write(f"📊 Total Workers: {status['total_workers']}")
        self.stdout.write(f"🟢 Running Workers: {status['running_workers']}")
        self.stdout.write(f"📅 Beat Running: {'✅' if status['beat_running'] else '❌'}")
        
        if status['workers']:
            self.stdout.write(f"\n📍 WORKER DETAILS:")
            for worker in status['workers']:
                status_icon = "🟢" if worker['running'] else "🔴"
                self.stdout.write(f"  {status_icon} {worker['name']}")
                self.stdout.write(f"     Location: {worker['location']}")
                self.stdout.write(f"     Queue: {worker['queue']}")
                self.stdout.write(f"     PID: {worker['pid']}")
        
        self.stdout.write(f"\n💡 COMMANDS:")
        self.stdout.write(f"  • Start workers: python manage.py worker_status --start")
        self.stdout.write(f"  • Stop workers: python manage.py worker_status --stop")
        self.stdout.write(f"  • Restart workers: python manage.py worker_status --restart")
