# Generated by Django 4.2.21 on 2025-07-09 20:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('queue_system', '0004_merge_20250620_0049'),
    ]

    operations = [
        migrations.AddField(
            model_name='queuedjob',
            name='error_details',
            field=models.JSONField(blank=True, help_text='Detailed error information', null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='error_message',
            field=models.TextField(blank=True, help_text='Last error message', null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='failure_reason',
            field=models.CharField(blank=True, help_text='Categorized failure reason', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='max_retries',
            field=models.PositiveIntegerField(default=3),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='requeue_priority',
            field=models.BooleanField(default=False, help_text='Process with high priority when requeued'),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='requeue_reason',
            field=models.TextField(blank=True, help_text='Reason for requeuing', null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='review_notes',
            field=models.TextField(blank=True, help_text='Admin notes about the failure', null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='reviewed_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='queuedjob',
            name='reviewed_by',
            field=models.CharField(blank=True, help_text='Admin who reviewed the job', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='locationqueueconfig',
            name='priority_level',
            field=models.PositiveIntegerField(default=1, help_text='Priority level for this location (higher numbers = higher priority)'),
        ),
        migrations.AlterField(
            model_name='queuedjob',
            name='status',
            field=models.CharField(choices=[('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('review', 'Under Review'), ('requeued', 'Requeued')], default='queued', max_length=20),
        ),
    ]
