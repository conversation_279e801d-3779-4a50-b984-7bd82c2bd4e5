#!/usr/bin/env python
"""
Test system status after fixing scheduler worker issues
"""

import os
import sys
import subprocess
import requests
import time

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

def check_redis():
    """Check if Redis is running"""
    try:
        result = subprocess.run(['redis-cli', 'ping'], capture_output=True, text=True, timeout=5)
        return result.returncode == 0 and 'PONG' in result.stdout
    except:
        return False

def check_celery_processes():
    """Count Celery processes"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            return len([line for line in lines if 'celery.exe' in line and line.strip()])
        return 0
    except:
        return 0

def check_django_server():
    """Check if Django server is running"""
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_admin_ui():
    """Check if admin UI is accessible"""
    try:
        response = requests.get('http://localhost:8000/queue/admin/queue-overview/', timeout=5)
        return response.status_code == 200
    except:
        return False

def get_database_stats():
    """Get database statistics"""
    try:
        from queue_system.models import LocationQueueConfig, QueuedJob
        from locations.models import Location
        
        total_locations = Location.objects.count()
        total_configs = LocationQueueConfig.objects.count()
        total_jobs = QueuedJob.objects.count()
        active_jobs = QueuedJob.objects.filter(status='processing').count()
        queued_jobs = QueuedJob.objects.filter(status='queued').count()
        
        return {
            'locations': total_locations,
            'configs': total_configs,
            'total_jobs': total_jobs,
            'active_jobs': active_jobs,
            'queued_jobs': queued_jobs
        }
    except Exception as e:
        return {'error': str(e)}

def test_sync_task():
    """Test the sync task manually"""
    try:
        from queue_system.tasks import sync_worker_status_task
        result = sync_worker_status_task()
        return {'success': True, 'result': result}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def main():
    print("🔍 SYSTEM STATUS CHECK")
    print("=" * 60)
    
    # Check Redis
    redis_status = check_redis()
    print(f"Redis Server: {'✅ Running' if redis_status else '❌ Not Running'}")
    
    # Check Celery processes
    celery_count = check_celery_processes()
    print(f"Celery Processes: {celery_count} running")
    
    # Check Django server
    django_status = check_django_server()
    print(f"Django Server: {'✅ Running' if django_status else '❌ Not Running'}")
    
    # Check admin UI
    admin_status = check_admin_ui()
    print(f"Admin UI: {'✅ Accessible' if admin_status else '❌ Not Accessible'}")
    
    print("\n📊 DATABASE STATISTICS")
    print("=" * 40)
    db_stats = get_database_stats()
    if 'error' not in db_stats:
        print(f"Locations: {db_stats['locations']}")
        print(f"Queue Configs: {db_stats['configs']}")
        print(f"Total Jobs: {db_stats['total_jobs']}")
        print(f"Active Jobs: {db_stats['active_jobs']}")
        print(f"Queued Jobs: {db_stats['queued_jobs']}")
    else:
        print(f"❌ Database Error: {db_stats['error']}")
    
    print("\n🔧 SYNC TASK TEST")
    print("=" * 40)
    sync_result = test_sync_task()
    if sync_result['success']:
        print(f"✅ Sync Task: {sync_result['result']}")
    else:
        print(f"❌ Sync Task Failed: {sync_result['error']}")
    
    print("\n🎯 SYSTEM HEALTH SUMMARY")
    print("=" * 50)
    
    all_good = redis_status and celery_count > 0 and django_status and admin_status
    
    if all_good:
        print("✅ ALL SYSTEMS OPERATIONAL")
        print("   • Redis: Connected")
        print("   • Celery: Workers running")
        print("   • Django: Server responding")
        print("   • Admin UI: Accessible")
        print("   • Database: Connected")
        
        if sync_result['success']:
            print("   • Sync Task: Working")
        else:
            print("   • Sync Task: Needs attention")
            
    else:
        print("⚠️  SOME ISSUES DETECTED")
        if not redis_status:
            print("   ❌ Redis not running")
        if celery_count == 0:
            print("   ❌ No Celery workers")
        if not django_status:
            print("   ❌ Django server not responding")
        if not admin_status:
            print("   ❌ Admin UI not accessible")
    
    print(f"\n🔗 QUICK LINKS")
    print("=" * 30)
    print("• Admin Overview: http://localhost:8000/queue/admin/queue-overview/")
    print("• API Stats: http://localhost:8000/queue/api/live-stats/")
    print("• Django Admin: http://localhost:8000/admin/")
    
    print(f"\n📋 NEXT STEPS")
    print("=" * 30)
    if all_good:
        print("1. ✅ System is ready for testing")
        print("2. 🔄 Re-enable live sync if needed")
        print("3. 🚀 Start location workers for processing")
        print("4. 📊 Monitor admin UI for real-time updates")
    else:
        print("1. 🔧 Fix the issues listed above")
        print("2. 🔄 Restart failed components")
        print("3. 🧪 Run this test again")

if __name__ == '__main__':
    main()
