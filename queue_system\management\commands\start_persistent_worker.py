from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time
import signal
import sys

class Command(BaseCommand):
    help = 'Start a persistent worker that always keeps running'

    def add_arguments(self, parser):
        parser.add_argument('--location', type=str, help='Process jobs for specific location only')
        parser.add_argument('--interval', type=int, default=30, help='Check interval in seconds (default: 30)')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING PERSISTENT WORKER"))
        self.stdout.write("=" * 60)
        self.stdout.write("This worker will keep running continuously")
        self.stdout.write("Press Ctrl+C to stop")
        
        location_filter = options.get('location')
        interval = options['interval']
        
        if location_filter:
            # Verify location exists
            locations = Location.objects.filter(location_name__icontains=location_filter)
            if not locations.exists():
                self.stdout.write(self.style.ERROR(f"No locations found matching '{location_filter}'"))
                return
            self.stdout.write(f"📍 Processing jobs for: {location_filter}")
        else:
            self.stdout.write(f"📍 Processing jobs for: ALL locations")
        
        self.stdout.write(f"⏰ Check interval: {interval} seconds")
        self.stdout.write("=" * 60)
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            self.stdout.write(f"\n\n🛑 Received shutdown signal")
            self.stdout.write(f"✅ Worker stopped gracefully")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Main worker loop
        cycle_count = 0
        
        try:
            while True:
                cycle_count += 1
                self.stdout.write(f"\n🔄 [Cycle {cycle_count}] Checking for jobs...")
                
                # Process jobs directly using the existing logic
                processed_count = self.process_jobs(location_filter)
                
                if processed_count > 0:
                    self.stdout.write(self.style.SUCCESS(f"✅ Processed {processed_count} jobs"))
                else:
                    self.stdout.write(f"💤 No jobs found - waiting {interval} seconds...")
                
                # Wait for next cycle
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write(f"\n\n🛑 Worker stopped by user")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"\n❌ Worker error: {str(e)}"))
            self.stdout.write(f"🔄 Restarting in 10 seconds...")
            time.sleep(10)
            # Restart the worker
            self.handle(*args, **options)

    def process_jobs(self, location_filter):
        """Process queued jobs"""
        from queue_system.models import QueuedJob
        from queue_system.tasks import process_order
        
        # Get queued jobs
        jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
        
        if location_filter:
            jobs = jobs.filter(location__location_name__icontains=location_filter)
        
        if not jobs.exists():
            return 0
        
        processed_count = 0
        max_jobs_per_cycle = 5  # Process up to 5 jobs per cycle
        
        for job in jobs[:max_jobs_per_cycle]:
            self.stdout.write(f"  🔧 Processing Job {job.id}: {job.order.first_name} {job.order.surname}")
            
            try:
                # Process the job
                result = process_order(str(job.order.id))
                
                if result:
                    self.stdout.write(f"    ✅ Job {job.id} completed successfully")
                else:
                    self.stdout.write(f"    ⚠️  Job {job.id} failed (will retry)")
                
                processed_count += 1
                
            except Exception as e:
                self.stdout.write(f"    ❌ Error processing Job {job.id}: {str(e)}")
                processed_count += 1
        
        return processed_count
