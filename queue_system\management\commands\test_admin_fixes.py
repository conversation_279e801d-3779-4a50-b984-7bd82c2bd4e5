from django.core.management.base import BaseCommand
from django.utils import timezone
from queue_system.models import Que<PERSON><PERSON><PERSON>, JobError
from queue_system.tasks import determine_failure_reason
from orders.models import order as Order
from locations.models import Location
import time

class Command(BaseCommand):
    help = 'Test the admin interface fixes'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🧪 TESTING ADMIN INTERFACE FIXES"))
        self.stdout.write("=" * 60)
        
        # Test 1: Error Categorization
        self.test_error_categorization()
        
        # Test 2: Current Job Status
        self.test_current_jobs()
        
        # Test 3: Requeue Functionality
        self.test_requeue_instructions()

    def test_error_categorization(self):
        self.stdout.write(f"\n🔍 TEST 1: ERROR CATEGORIZATION")
        self.stdout.write("-" * 40)
        
        # Test different error messages
        test_errors = [
            "No Barbados form data found for order abc123",
            "Missing form data for processing",
            "Failed to import Barbados bot",
            "Element not found on page",
            "Connection refused",
            "Authentication failed",
            "Invalid data format",
            "Missing date field in form",
            "Some random error message"
        ]
        
        # Create a dummy job for testing
        try:
            location = Location.objects.first()
            if not location:
                self.stdout.write(self.style.ERROR("No locations found. Please create a location first."))
                return
                
            order = Order.objects.first()
            if not order:
                self.stdout.write(self.style.ERROR("No orders found. Please create an order first."))
                return
                
            test_job = QueuedJob.objects.create(
                order=order,
                location=location,
                status='failed',
                max_retries=3,
                retry_count=3
            )
            
            self.stdout.write(f"📋 Testing error categorization:")
            
            for error_msg in test_errors:
                # Create a test error
                JobError.objects.create(
                    job=test_job,
                    error_message=error_msg
                )
                
                # Test categorization
                category = determine_failure_reason(test_job)
                
                # Clean up the error for next test
                JobError.objects.filter(job=test_job).delete()
                
                self.stdout.write(f"   '{error_msg[:50]}...' → {category}")
            
            # Clean up test job
            test_job.delete()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error in categorization test: {str(e)}"))

    def test_current_jobs(self):
        self.stdout.write(f"\n📊 TEST 2: CURRENT JOB STATUS")
        self.stdout.write("-" * 40)
        
        # Show current jobs with their error categorization
        failed_jobs = QueuedJob.objects.filter(status__in=['failed', 'review']).order_by('-created_at')[:5]
        
        if failed_jobs.exists():
            self.stdout.write(f"📋 Failed/Review Jobs ({failed_jobs.count()}):")
            for job in failed_jobs:
                self.stdout.write(f"\n🔍 Job {job.id}:")
                self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
                self.stdout.write(f"   Status: {job.status}")
                self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
                self.stdout.write(f"   Failure Reason: {job.failure_reason or 'Not set'}")
                
                # Show latest error
                latest_error = JobError.objects.filter(job=job).order_by('-occurred_at').first()
                if latest_error:
                    self.stdout.write(f"   Latest Error: {latest_error.error_message[:80]}...")
                    
                    # Test categorization on this job
                    category = determine_failure_reason(job)
                    self.stdout.write(f"   Categorized As: {category}")
                else:
                    self.stdout.write(f"   No errors recorded")
        else:
            self.stdout.write("✅ No failed or review jobs found")
        
        # Show queued jobs with retries
        retry_jobs = QueuedJob.objects.filter(retry_count__gt=0, status='queued').order_by('-created_at')[:3]
        
        if retry_jobs.exists():
            self.stdout.write(f"\n🔄 Jobs with Retries ({retry_jobs.count()}):")
            for job in retry_jobs:
                self.stdout.write(f"   Job {job.id}: {job.retry_count}/{job.max_retries} retries, status: {job.status}")

    def test_requeue_instructions(self):
        self.stdout.write(f"\n🔄 TEST 3: REQUEUE FUNCTIONALITY")
        self.stdout.write("-" * 40)
        
        failed_jobs = QueuedJob.objects.filter(status__in=['failed', 'review']).first()
        
        if failed_jobs:
            self.stdout.write(f"📋 Found job for requeue testing: Job {failed_jobs.id}")
            self.stdout.write(f"   Status: {failed_jobs.status}")
            self.stdout.write(f"   Customer: {failed_jobs.order.first_name} {failed_jobs.order.surname}")
            
            self.stdout.write(f"\n🎯 TO TEST REQUEUE:")
            self.stdout.write(f"1. Go to: http://localhost:8000/queue/admin/review-queue/")
            self.stdout.write(f"2. Find Job {failed_jobs.id} in the list")
            self.stdout.write(f"3. Click the '🔄 Requeue' button")
            self.stdout.write(f"4. Job should be reprocessed automatically")
            
            self.stdout.write(f"\n✅ EXPECTED BEHAVIOR:")
            self.stdout.write(f"   • Job status changes to 'queued'")
            self.stdout.write(f"   • Retry count resets to 0")
            self.stdout.write(f"   • Job starts processing within 5 seconds")
            self.stdout.write(f"   • Error message and failure reason cleared")
            
        else:
            self.stdout.write("ℹ️  No failed jobs available for requeue testing")
            self.stdout.write("   Create a test job with: python manage.py test_retry_mechanism --create-test")
        
        self.stdout.write(f"\n🎨 VISUAL IMPROVEMENTS:")
        self.stdout.write(f"   ✅ Error types now show proper names (not 'Unknown Error')")
        self.stdout.write(f"   ✅ Retry counts display correctly (3/3, not 4/3)")
        self.stdout.write(f"   ✅ Requeue button only shows for failed jobs")
        self.stdout.write(f"   ✅ Better CSS styling with color-coded badges")
        self.stdout.write(f"   ✅ Actual bot error messages displayed")
        
        self.stdout.write(f"\n🔧 FUNCTIONALITY FIXES:")
        self.stdout.write(f"   ✅ Requeue actually starts job processing")
        self.stdout.write(f"   ✅ Error categorization improved")
        self.stdout.write(f"   ✅ Review queue shows both failed and review jobs")
        self.stdout.write(f"   ✅ Job details show grouped error history")
        
        self.stdout.write(f"\n🌐 ACCESS DASHBOARDS:")
        self.stdout.write(f"   📊 Queue Overview: http://localhost:8000/queue/admin/queue-overview/")
        self.stdout.write(f"   🔍 Review Queue: http://localhost:8000/queue/admin/review-queue/")
        self.stdout.write(f"   🎛️ Admin Home: http://localhost:8000/admin/")
