from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Fix migration state for queue_system app'

    def handle(self, *args, **options):
        self.stdout.write("Checking migration records...")
        
        # Check if the migration record exists
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM django_migrations WHERE app='queue_system' AND name='0001_initial'")
            initial_migration = cursor.fetchone()
            
            if not initial_migration:
                self.stdout.write(self.style.WARNING("Initial migration record not found, creating it..."))
                
                # Insert the migration record
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW())",
                    ['queue_system', '0001_initial']
                )
                
                self.stdout.write(self.style.SUCCESS("Created migration record for 0001_initial"))
            else:
                self.stdout.write(self.style.SUCCESS("Initial migration record already exists"))
            
            # Check for the auto_scale fields migration
            cursor.execute("SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'")
            auto_scale_migration = cursor.fetchone()
            
            if not auto_scale_migration:
                # Check if the columns exist
                try:
                    cursor.execute("SELECT auto_scale, min_workers FROM queue_system_locationqueueconfig LIMIT 1")
                    columns_exist = True
                except:
                    columns_exist = False
                
                if columns_exist:
                    self.stdout.write(self.style.WARNING("Columns exist but migration record not found, creating it..."))
                    
                    # Insert the migration record
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW())",
                        ['queue_system', 'add_auto_scale_fields']
                    )
                    
                    self.stdout.write(self.style.SUCCESS("Created migration record for add_auto_scale_fields"))
                else:
                    self.stdout.write(self.style.WARNING("Columns don't exist, you should run the migration"))
            else:
                self.stdout.write(self.style.SUCCESS("Auto-scale fields migration record already exists"))
        
        self.stdout.write(self.style.SUCCESS("Migration state check complete"))