import json
import subprocess
from django.core.management.base import BaseCommand
from queue_system.models import Queued<PERSON>ob, LocationQueueConfig
from django.utils import timezone
import redis
from django.conf import settings
from orders.models import order as Order  # Fix the import with alias

class Command(BaseCommand):
    help = 'Diagnose queue issues and fix common problems'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix identified issues',
        )
        
        parser.add_argument(
            '--requeue',
            action='store_true',
            help='Requeue stuck jobs',
        )

    def handle(self, *args, **options):
        self.stdout.write("Diagnosing queue system...")
        
        # Check Celery workers
        self.check_celery_workers()
        
        # Check Redis queues
        self.check_redis_queues()
        
        # Check database jobs
        self.check_database_jobs(fix=options['fix'], requeue=options['requeue'])
        
        # Check routing configuration
        self.check_routing_configuration()
        
        self.stdout.write(self.style.SUCCESS("Diagnosis complete"))
    
    def check_celery_workers(self):
        self.stdout.write("\nChecking Celery workers...")
        
        try:
            # Run celery inspect active
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'active'],
                capture_output=True,
                text=True
            )
            
            if "No nodes replied within time constraint" in result.stdout:
                self.stdout.write(self.style.ERROR("No Celery workers are responding"))
            else:
                self.stdout.write(self.style.SUCCESS("Celery workers are active:"))
                self.stdout.write(result.stdout)
            
            # Run celery inspect registered
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'registered'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nRegistered tasks:")
            self.stdout.write(result.stdout)
            
            # Run celery inspect stats
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'stats'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nWorker stats:")
            self.stdout.write(result.stdout)
            
            # Run celery inspect active_queues
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'active_queues'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nActive queues:")
            self.stdout.write(result.stdout)
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking Celery workers: {str(e)}"))
    
    def check_redis_queues(self):
        self.stdout.write("\nChecking Redis queues...")
        
        try:
            # Connect to Redis
            r = redis.Redis.from_url(settings.CELERY_BROKER_URL)
            
            # Get all keys
            keys = r.keys('*')
            
            self.stdout.write(f"Found {len(keys)} keys in Redis")
            
            # Check for Celery queues
            celery_queues = [k.decode() for k in keys if k.decode().startswith('celery')]
            
            self.stdout.write(f"Found {len(celery_queues)} Celery-related keys")
            
            # Check each location queue
            configs = LocationQueueConfig.objects.all()
            
            for config in configs:
                location_id = config.location.id
                queue_name = f'location.{location_id}'
                
                # Check if queue exists in Redis
                queue_key = f'_kombu.binding.{queue_name}'
                if queue_key.encode() in keys:
                    self.stdout.write(self.style.SUCCESS(f"Queue for location {location_id} exists in Redis"))
                    
                    # Check queue length
                    queue_length = r.llen(f'celery')  # Celery uses a single list for all queues
                    self.stdout.write(f"Queue length: {queue_length}")
                else:
                    self.stdout.write(self.style.WARNING(f"Queue for location {location_id} does not exist in Redis"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking Redis queues: {str(e)}"))
    
    def check_database_jobs(self, fix=False, requeue=False):
        self.stdout.write("\nChecking database jobs...")
        
        # Check for stuck jobs (processing for too long)
        one_hour_ago = timezone.now() - timezone.timedelta(hours=1)
        stuck_jobs = QueuedJob.objects.filter(
            status='processing',
            started_at__lt=one_hour_ago
        )
        
        if stuck_jobs.exists():
            self.stdout.write(self.style.WARNING(f"Found {stuck_jobs.count()} stuck jobs (processing for > 1 hour)"))
            
            if fix or requeue:
                for job in stuck_jobs:
                    self.stdout.write(f"Resetting job {job.id} for order {job.order.id}")
                    job.status = 'queued'
                    job.save()
                    
                    # Requeue the job
                    from queue_system.tasks import process_order
                    process_order.apply_async(
                        args=[str(job.order.id)],
                        queue=f'location.{job.location.id}'
                    )
                
                self.stdout.write(self.style.SUCCESS(f"Reset {stuck_jobs.count()} stuck jobs"))
        else:
            self.stdout.write(self.style.SUCCESS("No stuck jobs found"))
        
        # Check for queued jobs
        queued_jobs = QueuedJob.objects.filter(status='queued')
        
        if queued_jobs.exists():
            self.stdout.write(f"Found {queued_jobs.count()} queued jobs")
            
            # Group by location
            location_counts = {}
            for job in queued_jobs:
                location_id = job.location.id
                if location_id not in location_counts:
                    location_counts[location_id] = 0
                location_counts[location_id] += 1
            
            for location_id, count in location_counts.items():
                self.stdout.write(f"Location {location_id}: {count} queued jobs")
                
                # Check if workers exist for this location
                config = LocationQueueConfig.objects.get(location_id=location_id)
                if config.active_workers == 0:
                    self.stdout.write(self.style.ERROR(f"No active workers for location {location_id}"))
                    
                    if fix:
                        self.stdout.write("Starting worker for this location...")
                        try:
                            # Start a worker for this location
                            subprocess.Popen(
                                [
                                    'celery', '-A', 'config', 'worker',
                                    '-Q', f'location.{location_id}',
                                    '-c', '1',
                                    '--loglevel=info',
                                    '-n', f'worker.location_{location_id}@%h',
                                    '--detach'
                                ],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE
                            )
                            
                            # Update active workers count
                            config.active_workers = 1
                            config.save()
                            
                            self.stdout.write(self.style.SUCCESS(f"Started worker for location {location_id}"))
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f"Error starting worker: {str(e)}"))
            
            if requeue:
                self.stdout.write("Requeuing all queued jobs...")
                
                for job in queued_jobs:
                    self.stdout.write(f"Requeuing job {job.id} for order {job.order.id}")
                    
                    # Requeue the job
                    from queue_system.tasks import process_order
                    process_order.apply_async(
                        args=[str(job.order.id)],
                        queue=f'location.{job.location.id}'
                    )
                
                self.stdout.write(self.style.SUCCESS(f"Requeued {queued_jobs.count()} jobs"))
        else:
            self.stdout.write(self.style.SUCCESS("No queued jobs found"))
    
    def check_routing_configuration(self):
        self.stdout.write("\nChecking routing configuration...")
        
        try:
            # Import the Celery app
            from config.celery import app
            
            # Check task routes
            task_routes = app.conf.task_routes
            
            self.stdout.write("Task routes configuration:")
            self.stdout.write(str(task_routes))
            
            # Check if process_order task is properly routed
            if 'queue_system.tasks.process_order' in task_routes:
                self.stdout.write(self.style.SUCCESS("process_order task is properly configured in task_routes"))
            else:
                self.stdout.write(self.style.ERROR("process_order task is not configured in task_routes"))
            
            # Check if the routing function is working
            if callable(task_routes.get('queue_system.tasks.process_order')):
                route_func = task_routes.get('queue_system.tasks.process_order')
                test_result = route_func('queue_system.tasks.process_order', ['123'], {}, {})
                self.stdout.write(f"Routing function test result: {test_result}")
                
                if test_result and 'queue' in test_result and test_result['queue'] == 'location.123':
                    self.stdout.write(self.style.SUCCESS("Routing function is working correctly"))
                else:
                    self.stdout.write(self.style.ERROR("Routing function is not working correctly"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking routing configuration: {str(e)}"))
