from django.core.management.base import BaseCommand
from queue_system.models import <PERSON><PERSON><PERSON><PERSON>, JobError

class Command(BaseCommand):
    help = 'Check Job 47 error display'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔍 CHECKING JOB 47 ERROR DISPLAY"))
        self.stdout.write("=" * 50)
        
        try:
            job = QueuedJob.objects.get(id=47)
            
            self.stdout.write(f"📋 JOB 47 DETAILS:")
            self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
            self.stdout.write(f"   Status: {job.status}")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"   Error Message: {job.error_message}")
            self.stdout.write(f"   Failure Reason: {job.failure_reason}")
            self.stdout.write(f"   Created: {job.created_at}")
            
            # Check JobError records
            errors = JobError.objects.filter(job=job).order_by('-occurred_at')
            self.stdout.write(f"\n🔍 JOB ERROR RECORDS ({errors.count()}):")
            
            for i, error in enumerate(errors, 1):
                self.stdout.write(f"   Error {i}:")
                self.stdout.write(f"     Message: {error.error_message}")
                self.stdout.write(f"     Occurred: {error.occurred_at}")
                if error.error_trace:
                    self.stdout.write(f"     Has Trace: Yes ({len(error.error_trace)} chars)")
                else:
                    self.stdout.write(f"     Has Trace: No")
            
            # Check what the admin UI would show
            self.stdout.write(f"\n🎨 ADMIN UI DISPLAY:")
            
            # This mimics the logic in job_details view
            actual_error_message = None
            if errors.exists():
                for error in errors:
                    msg = error.error_message
                    if (not msg.startswith('Retry ') and 
                        not msg.startswith('Max retries') and
                        not msg.startswith('Bot execution failed') and
                        msg != 'Bot execution failed'):
                        actual_error_message = msg
                        break
                
                if not actual_error_message and job.error_message:
                    if job.error_message.startswith('Retry ') and ': ' in job.error_message:
                        parts = job.error_message.split(': ', 1)
                        if len(parts) > 1:
                            actual_error_message = parts[1]
                    elif job.error_message.startswith('Max retries reached: '):
                        actual_error_message = job.error_message.replace('Max retries reached: ', '')
                    else:
                        actual_error_message = job.error_message
                
                if not actual_error_message:
                    actual_error_message = errors.first().error_message
            
            self.stdout.write(f"   Displayed Error: {actual_error_message}")
            
            # Check failure reason display
            failure_display = {
                'missing_form_data': 'Missing Form Data',
                'bot_configuration_error': 'Bot Configuration Error',
                'invalid_order_data': 'Invalid Order Data',
                'network_error': 'Network Error',
                'authentication_failed': 'Authentication Failed',
                'bot_execution_error': 'Bot Execution Error',
                'website_structure_changed': 'Website Structure Changed'
            }.get(job.failure_reason, job.failure_reason or 'Unknown Error')
            
            self.stdout.write(f"   Displayed Failure Reason: {failure_display}")
            
            # Analysis
            self.stdout.write(f"\n📊 ANALYSIS:")
            if actual_error_message == 'Bot execution failed':
                self.stdout.write(f"   ❌ Still showing generic error")
                self.stdout.write(f"   💡 This is because Job 47 was created before our fixes")
                self.stdout.write(f"   💡 The original bot error was never captured")
            elif actual_error_message and 'No Barbados form data found' in actual_error_message:
                self.stdout.write(f"   ✅ Showing specific error: Missing form data")
            elif actual_error_message and len(actual_error_message) > 20:
                self.stdout.write(f"   ✅ Showing detailed error message")
            else:
                self.stdout.write(f"   ⚠️  Error message needs improvement")
            
            self.stdout.write(f"\n🌐 VIEW IN ADMIN:")
            self.stdout.write(f"   Job Details: http://localhost:8000/queue/admin/job-details/47/")
            self.stdout.write(f"   Review Queue: http://localhost:8000/queue/admin/review-queue/")
            
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR("Job 47 not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
