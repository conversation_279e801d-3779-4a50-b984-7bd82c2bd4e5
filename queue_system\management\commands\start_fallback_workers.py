from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time

class Command(BaseCommand):
    help = 'Start fallback workers (no Redis required)'

    def add_arguments(self, parser):
        parser.add_argument('--location', type=str, help='Start workers for specific location only')
        parser.add_argument('--workers', type=int, default=2, help='Number of worker processes')
        parser.add_argument('--interval', type=int, default=30, help='Check interval in seconds')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING FALLBACK WORKERS"))
        self.stdout.write("=" * 60)
        self.stdout.write("This system processes jobs directly from the database")
        self.stdout.write("No Redis required - works in any environment")
        
        # Get locations
        if options['location']:
            locations = Location.objects.filter(location_name__icontains=options['location'])
            location_filter = f" for {options['location']}"
        else:
            locations = Location.objects.all()
            location_filter = ""
        
        if not locations.exists():
            self.stdout.write(self.style.ERROR("No locations found"))
            return
        
        workers_started = []
        num_workers = options['workers']
        interval = options['interval']
        
        self.stdout.write(f"\n🔄 Starting {num_workers} fallback workers{location_filter}...")
        
        for i in range(num_workers):
            worker_name = f"fallback_worker_{i+1}"
            
            self.stdout.write(f"  → Starting {worker_name}")
            
            # Build the auto worker command
            cmd = [
                'python', 'manage.py', 'auto_worker',
                f'--interval={interval}',
                '--max-jobs-per-cycle=5',
                f'--worker-name={worker_name}'
            ]
            
            # Add location filter if specified
            if options['location']:
                cmd.append(f'--location={options["location"]}')
            
            try:
                # Start the worker process
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'type': 'fallback',
                    'name': worker_name,
                    'location': options['location'] or 'all',
                    'pid': process.pid,
                    'process': process
                })
                
                self.stdout.write(f"    ✓ {worker_name} started (PID: {process.pid})")
                
                # Small delay between worker starts
                time.sleep(1)
                
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"    ⚠ {worker_name} failed: {e}"))
        
        # Summary
        self.stdout.write(f"\n" + "="*60)
        self.stdout.write(self.style.SUCCESS(f"🎉 FALLBACK WORKERS STARTED"))
        self.stdout.write(f"Started {len(workers_started)} workers:")
        
        for worker in workers_started:
            self.stdout.write(f"  🔄 {worker['name']}: {worker['location']} (PID: {worker['pid']})")
        
        self.stdout.write(f"\n📋 How it works:")
        self.stdout.write(f"  1. Workers check database every {interval} seconds")
        self.stdout.write(f"  2. Process up to 5 jobs per cycle")
        self.stdout.write(f"  3. Handle retries and failures automatically")
        self.stdout.write(f"  4. No Redis or external dependencies required")
        
        self.stdout.write(f"\n🔍 Monitoring:")
        self.stdout.write(f"  • Check jobs: python manage.py test_queue --list-jobs")
        self.stdout.write(f"  • Check processes: tasklist /FI \"IMAGENAME eq python.exe\"")
        self.stdout.write(f"  • Test system: python manage.py test_queue --create-test-order")
        self.stdout.write(f"  • Stop workers: taskkill /F /IM python.exe")
        
        self.stdout.write("="*60)
        
        # Keep the command running to monitor workers
        self.stdout.write(f"\n👀 MONITORING WORKERS (Press Ctrl+C to stop)")
        self.stdout.write("-" * 40)
        
        try:
            while True:
                time.sleep(10)
                
                # Check if workers are still running
                running_count = 0
                for worker in workers_started:
                    if worker['process'].poll() is None:
                        running_count += 1
                    else:
                        self.stdout.write(f"⚠️  Worker {worker['name']} stopped (PID: {worker['pid']})")
                
                if running_count == 0:
                    self.stdout.write(self.style.ERROR("❌ All workers stopped"))
                    break
                elif running_count < len(workers_started):
                    self.stdout.write(f"⚠️  {running_count}/{len(workers_started)} workers running")
                else:
                    self.stdout.write(f"✅ All {running_count} workers running")
                    
        except KeyboardInterrupt:
            self.stdout.write(f"\n\n🛑 STOPPING WORKERS...")
            
            for worker in workers_started:
                try:
                    worker['process'].terminate()
                    self.stdout.write(f"  ✓ Stopped {worker['name']} (PID: {worker['pid']})")
                except:
                    pass
            
            self.stdout.write(f"✅ All workers stopped")
