from django.core.management.base import BaseCommand
from queue_system.models import Que<PERSON><PERSON><PERSON>, JobError

class Command(BaseCommand):
    help = 'Fix Job 47 to show proper error message'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔧 FIXING JOB 47"))
        self.stdout.write("=" * 40)
        
        try:
            job = QueuedJob.objects.get(id=47)
            
            self.stdout.write(f"Before:")
            self.stdout.write(f"  Status: {job.status}")
            self.stdout.write(f"  Error Message: {job.error_message}")
            self.stdout.write(f"  Failure Reason: {job.failure_reason}")
            
            # Get the JobError
            error = JobError.objects.filter(job=job).first()
            if error:
                self.stdout.write(f"  JobError: {error.error_message}")
                
                # Extract actual error from "Max retries reached: Bot execution failed"
                if 'Max retries reached:' in error.error_message:
                    actual_error = error.error_message.replace('Max retries reached: ', '')
                    self.stdout.write(f"  Extracted: {actual_error}")
                    
                    # Update job with actual error
                    job.error_message = actual_error
                    job.status = 'failed'
                    job.save()
                    
                    # Also update the JobError to have the actual error
                    error.error_message = actual_error
                    error.save()
                    
                    self.stdout.write(f"\n✅ Updated Job 47:")
                    self.stdout.write(f"  Status: {job.status}")
                    self.stdout.write(f"  Error Message: {job.error_message}")
                    self.stdout.write(f"  Failure Reason: {job.failure_reason}")
                    
                else:
                    self.stdout.write("  No 'Max retries reached' wrapper found")
            else:
                self.stdout.write("  No JobError found")
                
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR("Job 47 not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
        
        self.stdout.write(f"\n🌐 View updated job:")
        self.stdout.write(f"   http://localhost:8000/queue/admin/job-details/47/")
