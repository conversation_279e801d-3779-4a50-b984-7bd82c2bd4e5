from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from queue_system.models import QueuedJob
from django.db import transaction, models

class Command(BaseCommand):
    help = 'Fix jobs that are stuck in processing status'

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='Show what would be fixed without making changes')
        parser.add_argument('--timeout-minutes', type=int, default=30, help='Consider jobs stuck after this many minutes')

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        timeout_minutes = options['timeout_minutes']
        
        self.stdout.write(self.style.SUCCESS("🔧 FIXING STUCK JOBS"))
        self.stdout.write("=" * 50)
        
        # Find jobs stuck in processing for more than timeout_minutes
        timeout_threshold = timezone.now() - timedelta(minutes=timeout_minutes)
        
        stuck_jobs = QueuedJob.objects.filter(
            status='processing',
            started_at__lt=timeout_threshold
        )
        
        if not stuck_jobs.exists():
            self.stdout.write(self.style.SUCCESS("✅ No stuck jobs found!"))
            return
        
        self.stdout.write(f"Found {stuck_jobs.count()} stuck jobs:")
        
        fixed_count = 0
        for job in stuck_jobs:
            time_stuck = timezone.now() - job.started_at
            hours_stuck = time_stuck.total_seconds() / 3600
            
            self.stdout.write(f"\n🔍 Job {job.id}:")
            self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
            self.stdout.write(f"   Started: {job.started_at}")
            self.stdout.write(f"   Stuck for: {hours_stuck:.1f} hours")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            
            if not dry_run:
                with transaction.atomic():
                    if job.retry_count < job.max_retries:
                        # Reset for retry
                        job.status = 'queued'
                        job.started_at = None
                        job.worker_id = None
                        job.retry_count += 1
                        job.save()
                        
                        self.stdout.write(f"   ✅ Reset to queued for retry {job.retry_count}")
                        fixed_count += 1
                    else:
                        # Max retries reached, mark as failed
                        job.mark_as_failed(
                            error_message=f"Job stuck in processing for {hours_stuck:.1f} hours",
                            failure_reason="processing_timeout"
                        )
                        
                        self.stdout.write(f"   ❌ Marked as failed (max retries reached)")
                        fixed_count += 1
            else:
                if job.retry_count < job.max_retries:
                    self.stdout.write(f"   🔄 Would reset to queued for retry")
                else:
                    self.stdout.write(f"   ❌ Would mark as failed (max retries reached)")
        
        if dry_run:
            self.stdout.write(f"\n💡 This was a dry run. Use --no-dry-run to actually fix jobs.")
        else:
            self.stdout.write(f"\n✅ Fixed {fixed_count} stuck jobs!")
        
        # Show current job status summary
        self.show_job_summary()

    def show_job_summary(self):
        self.stdout.write(f"\n📊 CURRENT JOB STATUS SUMMARY:")
        self.stdout.write("-" * 30)
        
        statuses = QueuedJob.objects.values('status').annotate(
            count=models.Count('id')
        ).order_by('status')
        
        for status in statuses:
            icon = {
                'queued': '⏳',
                'processing': '🔄',
                'completed': '✅',
                'failed': '❌',
                'review': '🔍',
                'requeued': '🔄',
                'cancelled': '🚫'
            }.get(status['status'], '❓')
            
            self.stdout.write(f"{icon} {status['status'].title()}: {status['count']}")
        
        # Show processing jobs details
        processing_jobs = QueuedJob.objects.filter(status='processing')
        if processing_jobs.exists():
            self.stdout.write(f"\n🔄 CURRENTLY PROCESSING JOBS:")
            for job in processing_jobs:
                if job.started_at:
                    time_running = timezone.now() - job.started_at
                    minutes_running = time_running.total_seconds() / 60
                    self.stdout.write(f"   Job {job.id}: Running for {minutes_running:.1f} minutes")
                else:
                    self.stdout.write(f"   Job {job.id}: No start time recorded")
        
        self.stdout.write(f"\n💡 TIP: Run this command regularly to prevent jobs from getting stuck.")
        self.stdout.write(f"💡 Consider setting up a cron job to run this automatically.")
