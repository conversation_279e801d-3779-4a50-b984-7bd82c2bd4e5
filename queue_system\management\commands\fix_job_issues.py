from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import models
from queue_system.models import Queued<PERSON>ob, JobError
from queue_system.tasks import determine_failure_reason
import traceback

class Command(BaseCommand):
    help = 'Fix job issues: retry counts, error categorization, and missing JobErrors'

    def add_arguments(self, parser):
        parser.add_argument('--job-id', type=int, help='Fix specific job ID')
        parser.add_argument('--all', action='store_true', help='Fix all jobs with issues')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔧 FIXING JOB ISSUES"))
        self.stdout.write("=" * 50)
        
        if options['job_id']:
            self.fix_specific_job(options['job_id'])
        elif options['all']:
            self.fix_all_jobs()
        else:
            self.show_issues()

    def fix_specific_job(self, job_id):
        try:
            job = QueuedJob.objects.get(id=job_id)
            self.stdout.write(f"\n🔍 FIXING JOB {job_id}")
            self.stdout.write("-" * 30)
            
            self.stdout.write(f"Before:")
            self.stdout.write(f"  Status: {job.status}")
            self.stdout.write(f"  Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"  Failure Reason: {job.failure_reason}")
            self.stdout.write(f"  Error Message: {job.error_message}")
            
            # Fix 1: Retry count
            if job.retry_count > job.max_retries:
                job.retry_count = job.max_retries
                self.stdout.write(f"✅ Fixed retry count: {job.retry_count}/{job.max_retries}")
            
            # Fix 2: Create missing JobError if needed
            job_errors = JobError.objects.filter(job=job)
            if not job_errors.exists() and job.error_message:
                # Extract the actual error message
                error_msg = job.error_message
                if error_msg.startswith('Retry '):
                    # Extract original error from retry message
                    parts = error_msg.split(': ', 1)
                    if len(parts) > 1:
                        error_msg = parts[1]
                
                JobError.objects.create(
                    job=job,
                    error_message=error_msg,
                    error_trace="Reconstructed from job error message"
                )
                self.stdout.write(f"✅ Created missing JobError: {error_msg}")
            
            # Fix 3: Update failure reason
            old_reason = job.failure_reason
            new_reason = determine_failure_reason(job)
            if old_reason != new_reason:
                job.failure_reason = new_reason
                self.stdout.write(f"✅ Updated failure reason: {old_reason} → {new_reason}")
            
            # Fix 4: If job is stuck in processing, move to failed
            if job.status == 'processing' and job.retry_count >= job.max_retries:
                job.status = 'failed'
                job.completed_at = timezone.now()
                self.stdout.write(f"✅ Moved stuck job to failed status")
            
            job.save()
            
            self.stdout.write(f"\nAfter:")
            self.stdout.write(f"  Status: {job.status}")
            self.stdout.write(f"  Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"  Failure Reason: {job.failure_reason}")
            
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))

    def fix_all_jobs(self):
        self.stdout.write(f"\n🔧 FIXING ALL JOBS WITH ISSUES")
        self.stdout.write("-" * 40)
        
        # Find jobs with issues
        problematic_jobs = QueuedJob.objects.filter(
            retry_count__gt=models.F('max_retries')
        ) | QueuedJob.objects.filter(
            failure_reason='unknown_error'
        ) | QueuedJob.objects.filter(
            status='processing',
            started_at__lt=timezone.now() - timezone.timedelta(minutes=30)
        )
        
        if not problematic_jobs.exists():
            self.stdout.write("✅ No problematic jobs found!")
            return
        
        for job in problematic_jobs:
            self.fix_specific_job(job.id)

    def show_issues(self):
        self.stdout.write(f"\n🔍 SCANNING FOR JOB ISSUES")
        self.stdout.write("-" * 30)
        
        # Issue 1: Retry count > max_retries
        bad_retry_jobs = QueuedJob.objects.filter(retry_count__gt=models.F('max_retries'))
        if bad_retry_jobs.exists():
            self.stdout.write(f"\n❌ Jobs with retry count > max_retries ({bad_retry_jobs.count()}):")
            for job in bad_retry_jobs:
                self.stdout.write(f"   Job {job.id}: {job.retry_count}/{job.max_retries}")
        
        # Issue 2: Unknown error reasons
        unknown_error_jobs = QueuedJob.objects.filter(failure_reason='unknown_error')
        if unknown_error_jobs.exists():
            self.stdout.write(f"\n❓ Jobs with unknown_error ({unknown_error_jobs.count()}):")
            for job in unknown_error_jobs:
                errors = JobError.objects.filter(job=job)
                self.stdout.write(f"   Job {job.id}: {errors.count()} errors recorded")
                if errors.exists():
                    latest = errors.first()
                    self.stdout.write(f"      Latest: {latest.error_message[:60]}...")
        
        # Issue 3: Jobs stuck in processing
        stuck_jobs = QueuedJob.objects.filter(
            status='processing',
            started_at__lt=timezone.now() - timezone.timedelta(minutes=30)
        )
        if stuck_jobs.exists():
            self.stdout.write(f"\n⏰ Jobs stuck in processing ({stuck_jobs.count()}):")
            for job in stuck_jobs:
                time_stuck = timezone.now() - job.started_at
                hours = time_stuck.total_seconds() / 3600
                self.stdout.write(f"   Job {job.id}: Stuck for {hours:.1f} hours")
        
        # Issue 4: Jobs with errors but no JobError records
        jobs_missing_errors = QueuedJob.objects.filter(
            error_message__isnull=False,
            status__in=['failed', 'review']
        ).exclude(
            id__in=JobError.objects.values_list('job_id', flat=True)
        )
        if jobs_missing_errors.exists():
            self.stdout.write(f"\n📝 Jobs missing JobError records ({jobs_missing_errors.count()}):")
            for job in jobs_missing_errors:
                self.stdout.write(f"   Job {job.id}: Has error_message but no JobError")
        
        if (bad_retry_jobs.exists() or unknown_error_jobs.exists() or 
            stuck_jobs.exists() or jobs_missing_errors.exists()):
            self.stdout.write(f"\n🔧 TO FIX ISSUES:")
            self.stdout.write(f"   Fix specific job: python manage.py fix_job_issues --job-id 47")
            self.stdout.write(f"   Fix all jobs: python manage.py fix_job_issues --all")
        else:
            self.stdout.write(f"\n✅ No issues found! All jobs look good.")
        
        # Show current job summary
        self.stdout.write(f"\n📊 CURRENT JOB SUMMARY:")
        statuses = QueuedJob.objects.values('status').annotate(count=models.Count('id'))
        for status in statuses:
            icon = {
                'queued': '⏳',
                'processing': '🔄', 
                'completed': '✅',
                'failed': '❌',
                'review': '🔍'
            }.get(status['status'], '❓')
            self.stdout.write(f"   {icon} {status['status']}: {status['count']}")
