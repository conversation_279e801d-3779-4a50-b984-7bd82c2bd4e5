from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Manually process the next job in the queue'

    def add_arguments(self, parser):
        parser.add_argument('--job-id', type=int, help='Process a specific job by ID')
        parser.add_argument('--location', type=str, help='Process next job for a specific location')

    def handle(self, *args, **options):
        if options['job_id']:
            self.process_specific_job(options['job_id'])
        else:
            self.process_next_job(options.get('location'))

    def process_specific_job(self, job_id):
        try:
            job = QueuedJob.objects.get(id=job_id)
            self.stdout.write(f"Processing job {job.id} for order {job.order.id}")
            
            # Call the task directly (synchronously)
            result = process_order(str(job.order.id))
            
            if result:
                self.stdout.write(self.style.SUCCESS(f"Job {job.id} processed successfully"))
            else:
                self.stdout.write(self.style.ERROR(f"Job {job.id} processing failed"))
                
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error processing job {job_id}: {str(e)}"))

    def process_next_job(self, location_name=None):
        # Get the next queued job
        jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
        
        if location_name:
            jobs = jobs.filter(location__location_name__icontains=location_name)
        
        if not jobs.exists():
            self.stdout.write("No queued jobs found")
            return
            
        job = jobs.first()
        self.stdout.write(f"Processing next job: {job.id} for order {job.order.id} ({job.order.first_name} {job.order.surname})")
        
        try:
            # Call the task directly (synchronously)
            result = process_order(str(job.order.id))
            
            if result:
                self.stdout.write(self.style.SUCCESS(f"Job {job.id} processed successfully"))
            else:
                self.stdout.write(self.style.ERROR(f"Job {job.id} processing failed"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error processing job {job.id}: {str(e)}"))
            import traceback
            self.stdout.write(traceback.format_exc())
