#!/bin/bash

# Get the current directory
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Create the systemd service file
cat > /tmp/queue_worker_manager.service << EOF
[Unit]
Description=Queue Worker Manager
After=network.target

[Service]
Type=simple
User=$(whoami)
Group=$(id -gn)
WorkingDirectory=$DIR
ExecStart=$DIR/venv/bin/python $DIR/manage.py manage_workers --interval=30
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# Install the service
sudo mv /tmp/queue_worker_manager.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable and start the service
sudo systemctl enable queue_worker_manager
sudo systemctl start queue_worker_manager

# Check status
sudo systemctl status queue_worker_manager