@echo off
echo 🚀 STARTING WORKERS FOR ALL LOCATIONS
echo ==========================================
echo This will start at least one worker for each location
echo Workers will restart automatically if they stop
echo Press Ctrl+C to stop all workers
echo ==========================================

python manage.py start_all_location_workers --workers-per-location=1 --interval=30

echo.
echo ✅ All workers stopped
pause
