@echo off
echo 🚀 STARTING WORKERS FOR ALL LOCATIONS
echo ==========================================
echo Choose your worker type:
echo 1. Auto Workers (no Redis required)
echo 2. Celery Workers (requires Redis)
echo ==========================================

set /p choice="Enter choice (1 or 2): "

if "%choice%"=="1" (
    echo Starting Auto Workers...
    python manage.py start_all_location_workers --workers-per-location=1 --interval=30
) else if "%choice%"=="2" (
    echo Starting Celery Workers...
    python manage.py start_all_location_workers --workers-per-location=1 --use-celery --concurrency=1 --beat
) else (
    echo Invalid choice. Starting Auto Workers by default...
    python manage.py start_all_location_workers --workers-per-location=1 --interval=30
)

echo.
echo ✅ All workers stopped
pause
