from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time
import redis

class Command(BaseCommand):
    help = 'Start production workers for all location queues (no monitoring)'

    def add_arguments(self, parser):
        parser.add_argument('--workers-per-location', type=int, default=1, help='Number of workers per location (default: 1)')
        parser.add_argument('--concurrency', type=int, default=1, help='Celery worker concurrency (default: 1)')
        parser.add_argument('--beat', action='store_true', help='Start Celery beat scheduler')
        parser.add_argument('--location', type=str, help='Start workers for specific location only')

    def check_redis_connection(self):
        """Check if Redis is available"""
        try:
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            return True
        except Exception as e:
            self.stdout.write(f"❌ Redis connection failed: {str(e)}")
            return False

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING PRODUCTION WORKERS"))
        self.stdout.write("=" * 60)
        
        # Check Redis connection first
        if not self.check_redis_connection():
            self.stdout.write(self.style.ERROR("❌ Redis is required for Celery workers"))
            self.stdout.write("💡 Start Redis server first")
            return
        else:
            self.stdout.write(self.style.SUCCESS("✅ Redis connection successful"))
        
        # Get locations
        if options['location']:
            locations = Location.objects.filter(location_name__icontains=options['location'])
        else:
            locations = Location.objects.all()
        
        if not locations.exists():
            self.stdout.write(self.style.ERROR("❌ No locations found"))
            return
        
        workers_per_location = options['workers_per_location']
        concurrency = options['concurrency']
        start_beat = options['beat']
        
        self.stdout.write(f"📍 Found {locations.count()} locations")
        self.stdout.write(f"👥 Starting {workers_per_location} worker(s) per location")
        self.stdout.write(f"🔧 Worker concurrency: {concurrency}")
        self.stdout.write("=" * 60)
        
        workers_started = 0
        
        # Start Celery workers for each location
        for location in locations:
            queue_name = f'location.{location.id}'
            
            for worker_num in range(workers_per_location):
                worker_name = f"prod_worker_{location.location_name}_{worker_num + 1}"
                
                self.stdout.write(f"🔄 Starting {worker_name} for {location.location_name}")
                self.stdout.write(f"   Queue: {queue_name}")
                
                # Build Celery worker command
                cmd = [
                    'celery', '-A', 'config', 'worker',
                    '--loglevel=info',
                    f'--concurrency={concurrency}',
                    f'--queues={queue_name}',
                    f'--hostname={worker_name}@%h',
                    '--without-gossip',
                    '--without-mingle',
                    '--without-heartbeat',
                    '--detach'  # Run in background
                ]
                
                try:
                    # Start the worker process in background
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        self.stdout.write(f"   ✅ {worker_name} started successfully")
                        workers_started += 1
                    else:
                        self.stdout.write(f"   ❌ Failed to start {worker_name}")
                        if result.stderr:
                            self.stdout.write(f"      Error: {result.stderr.strip()}")
                    
                except subprocess.TimeoutExpired:
                    self.stdout.write(f"   ✅ {worker_name} started (background)")
                    workers_started += 1
                except Exception as e:
                    self.stdout.write(f"   ❌ Failed to start {worker_name}: {e}")
                
                # Small delay between worker starts
                time.sleep(0.5)
        
        # Start Celery Beat if requested
        if start_beat:
            self.stdout.write(f"\n📅 Starting Celery Beat scheduler...")
            
            beat_cmd = [
                'celery', '-A', 'config', 'beat',
                '--loglevel=info',
                '--scheduler=django_celery_beat.schedulers:DatabaseScheduler',
                '--detach'
            ]
            
            try:
                result = subprocess.run(beat_cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    self.stdout.write(f"   ✅ Celery Beat started successfully")
                else:
                    self.stdout.write(f"   ❌ Failed to start Celery Beat")
                    if result.stderr:
                        self.stdout.write(f"      Error: {result.stderr.strip()}")
                        
            except subprocess.TimeoutExpired:
                self.stdout.write(f"   ✅ Celery Beat started (background)")
            except Exception as e:
                self.stdout.write(f"   ❌ Failed to start Celery Beat: {e}")
        
        # Summary
        self.stdout.write(f"\n" + "="*60)
        self.stdout.write(self.style.SUCCESS(f"🎉 PRODUCTION WORKERS STARTED"))
        self.stdout.write(f"Workers started: {workers_started}")
        self.stdout.write(f"Expected workers: {locations.count() * workers_per_location}")
        
        if start_beat:
            self.stdout.write(f"Celery Beat: Started")
        
        self.stdout.write(f"\n🔍 MONITORING:")
        self.stdout.write(f"  • Check processes: tasklist /FI \"IMAGENAME eq celery.exe\"")
        self.stdout.write(f"  • Check queues: http://localhost:8000/queue/admin/queue-overview/")
        self.stdout.write(f"  • Stop workers: taskkill /F /IM celery.exe")
        
        self.stdout.write(f"\n📊 QUEUE STRUCTURE:")
        for location in locations[:5]:  # Show first 5
            queue_name = f'location.{location.id}'
            self.stdout.write(f"  📍 {location.location_name}: {queue_name}")
        
        if locations.count() > 5:
            self.stdout.write(f"  ... and {locations.count() - 5} more locations")
        
        self.stdout.write("="*60)

        # Auto-sync worker status with database
        if workers_started > 0:
            self.stdout.write(f"\n🔄 Syncing worker status with database...")
            try:
                from django.core.management import call_command
                call_command('sync_worker_status')
                self.stdout.write(f"✅ Worker status synced")
            except Exception as e:
                self.stdout.write(f"⚠️ Failed to sync worker status: {e}")
                self.stdout.write(f"💡 Run manually: python manage.py sync_worker_status")
