import subprocess
from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig, QueuedJob

class Command(BaseCommand):
    help = 'Check the status of Celery workers and queued jobs'

    def handle(self, *args, **options):
        self.stdout.write("Checking Celery worker status...")
        
        # Run the celery status command
        try:
            result = subprocess.run(
                ['celery', '-A', 'config', 'status'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.stdout.write(self.style.SUCCESS("Celery workers are running:"))
                self.stdout.write(result.stdout)
            else:
                self.stdout.write(self.style.ERROR("No Celery workers are running"))
                self.stdout.write(result.stderr)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking worker status: {str(e)}"))
        
        # Check active workers in the database
        self.stdout.write("\nActive workers in database:")
        configs = LocationQueueConfig.objects.all()
        
        for config in configs:
            location_name = config.location.location_name
            location_id = config.location.id
            active = config.active_workers
            max_workers = config.max_workers
            
            # Count queued jobs for this location
            queued_count = QueuedJob.objects.filter(
                location=config.location,
                status='queued'
            ).count()
            
            if active > 0:
                self.stdout.write(self.style.SUCCESS(
                    f"{location_name} (ID: {location_id}): {active}/{max_workers} workers active, {queued_count} jobs queued"
                ))
            else:
                self.stdout.write(self.style.WARNING(
                    f"{location_name} (ID: {location_id}): No active workers (max: {max_workers}), {queued_count} jobs queued"
                ))
        
        # Check for running Celery processes
        self.stdout.write("\nRunning Celery processes:")
        try:
            result = subprocess.run(
                ['ps', 'aux', '|', 'grep', 'celery'],
                shell=True,
                capture_output=True,
                text=True
            )
            self.stdout.write(result.stdout)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking processes: {str(e)}"))
            
        # Check Redis queues
        self.stdout.write("\nChecking Redis queues:")
        try:
            # This requires redis-cli to be installed
            result = subprocess.run(
                ['redis-cli', 'keys', '*'],
                capture_output=True,
                text=True
            )
            self.stdout.write(result.stdout)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking Redis: {str(e)}"))
