from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
import time
import signal
import sys

class Command(BaseCommand):
    help = 'Start one global worker that processes jobs for ALL locations'

    def add_arguments(self, parser):
        parser.add_argument('--interval', type=int, default=30, help='Check interval in seconds (default: 30)')
        parser.add_argument('--max-jobs-per-cycle', type=int, default=10, help='Max jobs per cycle (default: 10)')

    def handle(self, *args, **options):
        interval = options['interval']
        max_jobs = options['max_jobs_per_cycle']
        
        self.stdout.write(self.style.SUCCESS("🚀 STARTING GLOBAL WORKER"))
        self.stdout.write("=" * 60)
        self.stdout.write("This worker processes jobs for ALL locations")
        self.stdout.write(f"⏰ Check interval: {interval} seconds")
        self.stdout.write(f"🔧 Max jobs per cycle: {max_jobs}")
        self.stdout.write("Press Ctrl+C to stop")
        self.stdout.write("=" * 60)
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            self.stdout.write(f"\n\n🛑 Global worker stopped by user")
            self.stdout.write(f"✅ Shutdown complete")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        cycle = 0
        
        try:
            while True:
                cycle += 1
                current_time = time.strftime('%H:%M:%S')
                self.stdout.write(f"\n🔄 [{current_time}] Cycle {cycle}: Checking ALL locations for jobs...")
                
                # Get all queued jobs (all locations)
                jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
                job_count = jobs.count()
                
                if job_count > 0:
                    self.stdout.write(f"  📋 Found {job_count} queued jobs across all locations")
                    
                    # Group jobs by location for display
                    location_jobs = {}
                    for job in jobs[:max_jobs]:
                        loc_name = job.location.location_name if job.location else 'Unknown'
                        if loc_name not in location_jobs:
                            location_jobs[loc_name] = []
                        location_jobs[loc_name].append(job)
                    
                    # Show jobs by location
                    for location, loc_jobs in location_jobs.items():
                        self.stdout.write(f"  📍 {location}: {len(loc_jobs)} jobs")
                    
                    # Process jobs
                    processed = 0
                    for job in jobs[:max_jobs]:
                        location_name = job.location.location_name if job.location else 'Unknown'
                        customer_name = f"{job.order.first_name} {job.order.surname}"
                        
                        self.stdout.write(f"  🔧 Processing Job {job.id} ({location_name}): {customer_name}")
                        
                        try:
                            result = process_order(str(job.order.id))
                            if result:
                                self.stdout.write(f"    ✅ Success")
                            else:
                                self.stdout.write(f"    ⚠️  Failed (will retry)")
                            processed += 1
                        except Exception as e:
                            self.stdout.write(f"    ❌ Error: {str(e)}")
                            processed += 1
                    
                    self.stdout.write(f"  ✅ Processed {processed} jobs")
                else:
                    self.stdout.write(f"  💤 No jobs found across all locations")
                
                self.stdout.write(f"  ⏰ Waiting {interval} seconds...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            signal_handler(None, None)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"\n❌ Worker error: {str(e)}"))
            self.stdout.write(f"🔄 Restarting in 10 seconds...")
            time.sleep(10)
            # Restart
            self.handle(*args, **options)
