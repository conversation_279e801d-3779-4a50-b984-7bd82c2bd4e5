#!/usr/bin/env python
"""
Development Server with Auto-Starting Workers
Run this instead of 'python manage.py runserver' to automatically start workers
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Global variables
workers = []
beat_process = None
server_process = None

def check_redis():
    """Check if Redis is available"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except:
        return False

def start_workers():
    """Start Celery workers for each location"""
    global workers, beat_process
    
    print("🚀 Starting location workers...")
    
    if not check_redis():
        print("⚠️  Redis not available - workers will use fallback processing")
        return
    
    # Setup Django
    import django
    django.setup()
    
    from locations.models import Location
    
    # Start workers for each location
    locations = Location.objects.all()
    
    for location in locations:
        queue_name = f'location.{location.id}'
        worker_name = f'dev_worker_{location.location_name}_{location.id}'
        
        cmd = [
            'celery', '-A', 'config', 'worker',
            '--loglevel=info',
            '--concurrency=1',
            f'--queues={queue_name}',
            f'--hostname={worker_name}@%h',
            '--without-gossip',
            '--without-mingle',
            '--without-heartbeat'
        ]
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            workers.append({
                'name': worker_name,
                'location': location.location_name,
                'process': process,
                'pid': process.pid
            })
            print(f"✅ Started worker {worker_name} (PID: {process.pid})")
        except Exception as e:
            print(f"❌ Failed to start worker for {location.location_name}: {e}")
    
    # Start Celery Beat
    try:
        beat_cmd = ['celery', '-A', 'config', 'beat', '--loglevel=info']
        beat_process = subprocess.Popen(beat_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"📅 Started Celery Beat (PID: {beat_process.pid})")
    except Exception as e:
        print(f"❌ Failed to start Celery Beat: {e}")
    
    print(f"🎉 Started {len(workers)} workers + Beat scheduler")

def start_django_server():
    """Start Django development server"""
    global server_process
    
    print("🌐 Starting Django development server...")
    
    cmd = [sys.executable, 'manage.py', 'runserver', '8000']
    server_process = subprocess.Popen(cmd)
    print(f"✅ Django server started (PID: {server_process.pid})")
    
    return server_process

def stop_all():
    """Stop all processes"""
    global workers, beat_process, server_process
    
    print("\n🛑 Stopping all processes...")
    
    # Stop workers
    for worker in workers:
        try:
            worker['process'].terminate()
            print(f"✅ Stopped {worker['name']}")
        except:
            pass
    
    # Stop Beat
    if beat_process:
        try:
            beat_process.terminate()
            print("✅ Stopped Celery Beat")
        except:
            pass
    
    # Stop Django server
    if server_process:
        try:
            server_process.terminate()
            print("✅ Stopped Django server")
        except:
            pass

def signal_handler(sig, frame):
    """Handle Ctrl+C"""
    stop_all()
    sys.exit(0)

def main():
    """Main function"""
    print("🚀 DEVELOPMENT SERVER WITH AUTO-WORKERS")
    print("=" * 50)
    print("This will start:")
    print("  • Django development server on http://localhost:8000")
    print("  • Celery workers for each location queue")
    print("  • Celery Beat scheduler")
    print("Press Ctrl+C to stop all")
    print("=" * 50)
    
    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start workers in background thread
        worker_thread = threading.Thread(target=start_workers, daemon=True)
        worker_thread.start()
        
        # Wait a bit for workers to start
        time.sleep(3)
        
        # Start Django server (this will block)
        server = start_django_server()
        
        print("\n✅ All services started!")
        print("🌐 Django server: http://localhost:8000")
        print("📊 Admin: http://localhost:8000/admin")
        print("🔍 Queue admin: http://localhost:8000/queue/admin/queue-overview/")
        
        # Wait for server to finish
        server.wait()
        
    except KeyboardInterrupt:
        signal_handler(None, None)
    except Exception as e:
        print(f"❌ Error: {e}")
        stop_all()

if __name__ == '__main__':
    main()
