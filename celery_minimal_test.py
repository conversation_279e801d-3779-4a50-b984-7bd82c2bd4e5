# celery_minimal_test.py
from celery import Celery, shared_task

app = Celery('celery_minimal_test', broker='redis://localhost:6379/0')

def route_test_task(name, args, kwargs, options, task=None, **kw):
    print(f"[Router] Called for task: {name}")
    if name == 'celery_minimal_test.add':
        return {'queue': 'default'}
    return None

app.conf.task_routes = {
    'celery_minimal_test.add': route_test_task
}

@shared_task(name='celery_minimal_test.add')
def add(x, y):
    print(f"🔢 Executing add({x}, {y})")
    return x + y

if __name__ == '__main__':
    print("This should not run when importing for Celery.")
