from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig
from locations.models import Location
from celery import Celery
import re

class Command(BaseCommand):
    help = 'Sync database worker status with actual Celery workers'

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')

    def get_celery_workers(self):
        """Get list of active Celery workers"""
        try:
            from config.celery import app
            
            # Get active workers
            inspect = app.control.inspect()
            active_workers = inspect.active()
            
            if not active_workers:
                return {}
            
            # Parse worker names to extract location info
            location_workers = {}
            
            for worker_name in active_workers.keys():
                # Extract location from worker name patterns like:
                # celery_worker_Barbados-90d8c3f9_1@DESKTOP-DIDRRR6
                # prod_worker_Barbados_1@DESKTOP-DIDRRR6
                
                # Remove hostname part
                worker_base = worker_name.split('@')[0]
                
                # Extract location name from worker name
                if 'celery_worker_' in worker_base:
                    # Pattern: celery_worker_LocationName_Number
                    location_part = worker_base.replace('celery_worker_', '').rsplit('_', 1)[0]
                elif 'prod_worker_' in worker_base:
                    # Pattern: prod_worker_LocationName_Number
                    location_part = worker_base.replace('prod_worker_', '').rsplit('_', 1)[0]
                elif 'auto_worker_' in worker_base:
                    # Pattern: auto_worker_LocationName_LocationId
                    location_part = worker_base.replace('auto_worker_', '').rsplit('_', 1)[0]
                else:
                    continue
                
                if location_part not in location_workers:
                    location_workers[location_part] = 0
                location_workers[location_part] += 1
            
            return location_workers
            
        except Exception as e:
            self.stdout.write(f"Error getting Celery workers: {e}")
            return {}

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(self.style.SUCCESS("🔄 SYNCING WORKER STATUS"))
        self.stdout.write("=" * 50)
        
        if dry_run:
            self.stdout.write("🔍 DRY RUN MODE - No changes will be made")
        
        # Get actual Celery workers
        celery_workers = self.get_celery_workers()
        
        if not celery_workers:
            self.stdout.write(self.style.WARNING("⚠️ No Celery workers found"))
            return
        
        self.stdout.write(f"📊 Found Celery workers for {len(celery_workers)} locations:")
        for location_name, count in celery_workers.items():
            self.stdout.write(f"  • {location_name}: {count} workers")
        
        # Get all location configs
        configs = LocationQueueConfig.objects.select_related('location').all()
        
        updated_count = 0
        created_count = 0
        
        self.stdout.write(f"\n🔄 Updating database worker status:")
        
        for config in configs:
            location_name = config.location.location_name
            current_active = config.active_workers
            
            # Find matching Celery workers
            celery_count = 0
            for celery_location, count in celery_workers.items():
                if celery_location == location_name or celery_location in location_name:
                    celery_count = count
                    break
            
            if current_active != celery_count:
                self.stdout.write(f"  📍 {location_name}:")
                self.stdout.write(f"     Database: {current_active} workers")
                self.stdout.write(f"     Celery: {celery_count} workers")
                
                if not dry_run:
                    config.active_workers = celery_count
                    config.save()
                    self.stdout.write(f"     ✅ Updated to {celery_count} workers")
                else:
                    self.stdout.write(f"     🔍 Would update to {celery_count} workers")
                
                updated_count += 1
            else:
                self.stdout.write(f"  ✅ {location_name}: {current_active} workers (already correct)")
        
        # Check for locations without configs
        all_locations = Location.objects.all()
        for location in all_locations:
            if not hasattr(location, 'queue_config'):
                celery_count = 0
                for celery_location, count in celery_workers.items():
                    if celery_location == location.location_name or celery_location in location.location_name:
                        celery_count = count
                        break
                
                self.stdout.write(f"  📍 {location.location_name} (NEW CONFIG):")
                self.stdout.write(f"     Celery: {celery_count} workers")
                
                if not dry_run:
                    LocationQueueConfig.objects.create(
                        location=location,
                        max_workers=max(1, celery_count),
                        active_workers=celery_count
                    )
                    self.stdout.write(f"     ✅ Created config with {celery_count} workers")
                else:
                    self.stdout.write(f"     🔍 Would create config with {celery_count} workers")
                
                created_count += 1
        
        # Summary
        self.stdout.write(f"\n" + "="*50)
        if dry_run:
            self.stdout.write(self.style.SUCCESS("🔍 DRY RUN COMPLETE"))
            self.stdout.write(f"Would update: {updated_count} locations")
            self.stdout.write(f"Would create: {created_count} configs")
            self.stdout.write(f"\nRun without --dry-run to apply changes")
        else:
            self.stdout.write(self.style.SUCCESS("✅ SYNC COMPLETE"))
            self.stdout.write(f"Updated: {updated_count} locations")
            self.stdout.write(f"Created: {created_count} configs")
            
            self.stdout.write(f"\n🔍 Check admin UI: http://localhost:8000/queue/admin/queue-overview/")
        
        self.stdout.write("="*50)
