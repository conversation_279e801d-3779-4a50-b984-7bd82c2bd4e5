import os
import subprocess
import signal
import time
from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig, QueuedJob
import psutil
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Automatically manage Celery workers based on configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Check interval in seconds (default: 60)',
        )
        
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Run in debug mode with more verbose output',
        )
        
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='Run as a daemon process',
        )

    def handle(self, *args, **options):
        interval = options['interval']
        debug = options['debug']
        daemon = options['daemon']
        
        if daemon:
            # Run as a daemon process
            self.stdout.write("Starting worker manager in daemon mode")
            
            # Fork a child process
            pid = os.fork()
            
            if pid > 0:
                # Parent process
                self.stdout.write(f"Worker manager daemon started with PID: {pid}")
                return
            
            # Child process continues
            os.setsid()
            
            # Close standard file descriptors
            os.close(0)
            os.close(1)
            os.close(2)
            
            # Redirect standard file descriptors to /dev/null
            os.open('/dev/null', os.O_RDWR)
            os.dup2(0, 1)
            os.dup2(0, 2)
        
        # Main loop
        self.stdout.write(f"Worker manager started, checking every {interval} seconds")
        
        try:
            while True:
                self.manage_workers(debug)
                time.sleep(interval)
        except KeyboardInterrupt:
            self.stdout.write("Worker manager stopped")
    
    def manage_workers(self, debug):
        """
        Check and adjust worker counts based on configuration
        """
        # Get all location configs
        configs = LocationQueueConfig.objects.all()
        
        # Get current Celery processes
        celery_processes = self.get_celery_processes()
        
        for config in configs:
            location = config.location
            location_id = location.id
            location_name = location.location_name
            
            # Count queued jobs for this location
            queued_count = QueuedJob.objects.filter(
                location=location,
                status='queued'
            ).count()
            
            # Count current workers for this location
            current_workers = 0
            location_workers = []
            
            for proc in celery_processes:
                # Check if this is a worker for this location
                if f'location.{location_id}' in ' '.join(proc['cmdline']):
                    current_workers += 1
                    location_workers.append(proc)
            
            # Update active workers count in database
            config.active_workers = current_workers
            config.save()
            
            # Determine if we need to adjust worker count
            target_workers = config.max_workers
            
            if queued_count > 0 and current_workers < target_workers:
                # Need to start more workers
                workers_to_start = target_workers - current_workers
                logger.info(f"Starting {workers_to_start} worker(s) for {location_name} (Queue: location.{location_id})")
                
                for i in range(workers_to_start):
                    self.start_worker(location_id, location_name, debug)
            
            elif queued_count == 0 and current_workers > 0:
                # Can stop some workers if no jobs are queued
                # Keep at least one worker running
                workers_to_stop = current_workers - 1
                
                if workers_to_stop > 0:
                    logger.info(f"Stopping {workers_to_stop} worker(s) for {location_name}")
                    
                    for i in range(workers_to_stop):
                        if i < len(location_workers):
                            self.stop_worker(location_workers[i])
    
    def get_celery_processes(self):
        """
        Get all running Celery worker processes
        """
        celery_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'celery' in proc.info['name'] and 'worker' in ' '.join(proc.info['cmdline']):
                    celery_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': proc.info['cmdline']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        return celery_processes
    
    def start_worker(self, location_id, location_name, debug):
        """
        Start a new worker for the specified location
        """
        # Build the Celery worker command
        cmd = [
            'celery', 
            '-A', 'config', 
            'worker',
            '-Q', f'location.{location_id}',
            '-c', '1',
            '--loglevel=info' if not debug else '--loglevel=debug',
            '-n', f'worker.{location_name.replace(" ", "_")}_{int(time.time())}@%h',
            '--detach'
        ]
        
        # Start the worker process
        try:
            subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            logger.info(f"Started worker for location: {location_name} (Queue: location.{location_id})")
            return True
        except Exception as e:
            logger.error(f"Error starting worker for location {location_id}: {str(e)}")
            return False
    
    def stop_worker(self, worker_proc):
        """
        Stop a worker process
        """
        try:
            os.kill(worker_proc['pid'], signal.SIGTERM)
            logger.info(f"Sent SIGTERM to worker process {worker_proc['pid']}")
            return True
        except Exception as e:
            logger.error(f"Error stopping worker process {worker_proc['pid']}: {str(e)}")
            return False