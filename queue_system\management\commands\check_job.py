from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob

class Command(BaseCommand):
    help = 'Check a specific job status'

    def add_arguments(self, parser):
        parser.add_argument('job_id', type=int, help='Job ID to check')

    def handle(self, *args, **options):
        job_id = options['job_id']
        
        try:
            job = QueuedJob.objects.get(id=job_id)
            self.stdout.write(f"Job {job.id}: Order {job.order.id} - Status: {job.status}")
            self.stdout.write(f"Created: {job.created_at}")
            if job.started_at:
                self.stdout.write(f"Started: {job.started_at}")
            if job.completed_at:
                self.stdout.write(f"Completed: {job.completed_at}")
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
