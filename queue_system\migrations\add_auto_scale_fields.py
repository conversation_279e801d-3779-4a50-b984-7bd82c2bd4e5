from django.db import migrations, models

class Migration(migrations.Migration):

    dependencies = [
        ('queue_system', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='locationqueueconfig',
            name='auto_scale',
            field=models.BooleanField(default=True, help_text='Automatically scale workers based on queue length'),
        ),
        migrations.AddField(
            model_name='locationqueueconfig',
            name='min_workers',
            field=models.PositiveIntegerField(default=0, help_text='Minimum number of workers to keep running'),
        ),
    ]