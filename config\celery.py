import os
from celery import Celery
from kombu import Queue, Exchange
import logging

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
logger = logging.getLogger(__name__)

# Set the default Django settings module for the 'celery' program.

app = Celery('config')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# Explicitly ensure Redis configuration is loaded
from django.conf import settings
app.conf.broker_url = settings.CELERY_BROKER_URL
app.conf.result_backend = settings.CELERY_RESULT_BACKEND

# Define default queues
app.conf.task_default_queue = 'default'
app.conf.task_default_exchange = 'default'
app.conf.task_default_routing_key = 'default'

# Define a routing function


# Define queues explicitly
# app.conf.task_queues = (
#     Queue('default', Exchange('default'), routing_key='default'),
#     Queue('scheduler', Exchange('scheduler'), routing_key='scheduler'),
#     Queue('error', Exchange('error'), routing_key='error'),
#     # Dynamic location queues will be created as needed
# )

# from locations.models import Location
# def get_location_queues():
#     """Dynamically create queues for all locations"""
#     location_queues = [
#         Queue(f'location.{loc.id}', routing_key=f'location.{loc.id}')
#         for loc in Location.objects.all()
#     ]
#     return location_queues

app.conf.task_queues = (
    Queue('default'),
    Queue('scheduler'),
    Queue('error'),
    # *get_location_queues()
)

# Configure task routing
app.conf.task_routes = {
    # 'queue_system.tasks.process_order': {'queue': 'scheduler'},
    # 'config.tasks.process_order': 'queue_system.tasks.process_order',
    # 'config.tasks.process_order': route_process_order,
    # 'config.tasks.process_order': {'queue': 'scheduler'},
    'queue_system.routers.route_process_order'
    # 'queue_system.tasks.check_waiting_queue': {'queue': 'scheduler'},
    # 'queue_system.tasks.process_failed_job': {'queue': 'error'},
    # 'queue_system.tasks.schedule_job': {'queue': 'scheduler'},
}

# Configure task routes for location-based queues
app.conf.task_routes = [
    'queue_system.routers.route_process_order'
]

app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']
app.conf.task_default_queue = 'default'
app.conf.task_create_missing_queues = True

app.conf.update(
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    worker_prefetch_multiplier=1,  # Reduce prefetching for fair distribution
    task_acks_late=True,  # Acknowledge tasks after completion
    task_always_eager=False,  # Ensure we're not in test mode
    task_create_missing_queues=True,  # Auto-create queues
    task_default_queue='default',
    task_default_exchange='default',
    task_default_routing_key='default',
    task_track_started=True,
    task_reject_on_worker_lost=True,  # Reject tasks if worker dies
    worker_disable_rate_limits=False,
)

# app.conf.task_routes = {
#     'queue_system.tasks.check_waiting_queue': {'queue': 'scheduler'},
#     'queue_system.tasks.process_failed_job': {'queue': 'error'},
#     'queue_system.tasks.schedule_job': {'queue': 'scheduler'},
#     'queue_system.tasks.process_order': {'queue': 'default'},  # temporary
# }

app.conf.beat_schedule = {
    'check-waiting-queue-every-minute': {
        'task': 'queue_system.tasks.check_waiting_queue',
        'schedule': 60.0,  # Every minute
        'options': {'queue': 'scheduler'}
    },
    'sync-worker-status-every-5-seconds': {
        'task': 'queue_system.tasks.sync_worker_status_task',
        'schedule': 5.0,  # Every 5 seconds
        'options': {'queue': 'scheduler'}
    },
    'process-queued-jobs-every-30-seconds': {
        'task': 'queue_system.tasks.process_queued_jobs',
        'schedule': 30.0,  # Every 30 seconds
        'options': {'queue': 'scheduler'}
    },
}






