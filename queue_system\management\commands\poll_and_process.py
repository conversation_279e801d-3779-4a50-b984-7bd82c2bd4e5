from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
import time
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Poll for queued jobs and process them (simulates worker behavior)'

    def add_arguments(self, parser):
        parser.add_argument('--interval', type=int, default=5, help='Polling interval in seconds')
        parser.add_argument('--max-jobs', type=int, default=1, help='Maximum jobs to process')
        parser.add_argument('--location', type=str, help='Only process jobs for specific location')

    def handle(self, *args, **options):
        interval = options['interval']
        max_jobs = options['max_jobs']
        location_filter = options.get('location')
        
        self.stdout.write(f"Starting job polling (interval: {interval}s, max jobs: {max_jobs})")
        
        processed_count = 0
        
        try:
            while processed_count < max_jobs:
                # Get next queued job
                jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
                
                if location_filter:
                    jobs = jobs.filter(location__location_name__icontains=location_filter)
                
                if jobs.exists():
                    job = jobs.first()
                    self.stdout.write(f"Found job {job.id} for order {job.order.id}")
                    
                    try:
                        # Process the job directly
                        result = process_order(str(job.order.id))
                        
                        if result:
                            self.stdout.write(self.style.SUCCESS(f"Job {job.id} completed successfully"))
                        else:
                            self.stdout.write(self.style.ERROR(f"Job {job.id} failed"))
                            
                        processed_count += 1
                        
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Error processing job {job.id}: {str(e)}"))
                        processed_count += 1  # Count failed jobs too
                        
                else:
                    self.stdout.write("No queued jobs found")
                    
                if processed_count < max_jobs:
                    self.stdout.write(f"Waiting {interval} seconds...")
                    time.sleep(interval)
                    
        except KeyboardInterrupt:
            self.stdout.write("\nPolling stopped by user")
            
        self.stdout.write(f"Processed {processed_count} jobs")
