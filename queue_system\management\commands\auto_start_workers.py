from django.core.management.base import BaseCommand
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Auto-start workers when Django starts (called from celery.py)'

    def handle(self, *args, **options):
        """This command is called automatically from celery.py"""
        
        if not settings.DEBUG:
            self.stdout.write("⚠️ Auto-start workers only enabled in DEBUG mode")
            return
        
        try:
            # Import the function from celery.py
            from config.celery import start_location_workers
            
            self.stdout.write("🚀 Auto-starting location workers from Django...")
            start_location_workers()
            self.stdout.write("✅ Workers auto-started successfully")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Failed to auto-start workers: {e}"))
