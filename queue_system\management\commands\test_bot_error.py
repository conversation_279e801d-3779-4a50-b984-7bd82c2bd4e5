from django.core.management.base import BaseCommand
from django.utils import timezone
from orders.models import order as Order
from locations.models import Location
from barbados.models import BarbadosForm
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
import uuid

class Command(BaseCommand):
    help = 'Create a test job with form data to trigger actual bot execution error'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🧪 CREATING TEST JOB WITH FORM DATA"))
        self.stdout.write("=" * 60)
        
        # Get Barbados location
        barbados_location = Location.objects.filter(location_name__icontains='barbados').first()
        if not barbados_location:
            self.stdout.write(self.style.ERROR("No Barbados location found"))
            return
        
        # Create test order
        order = Order.objects.create(
            id=str(uuid.uuid4()),
            first_name="Bot",
            surname="Test",
            customer_email="<EMAIL>",
            location=barbados_location,
            status='criminal_check_passed'
        )
        
        # Create BarbadosForm with test data
        barbados_form = BarbadosForm.objects.create(
            order=order,
            passport_number="TEST123456",
            nationality="Test Country",
            place_of_birth="Test City",
            father_name="Test Father",
            mother_name="Test Mother",
            occupation="Test Occupation",
            employer="Test Employer",
            purpose_of_visit="Tourism",
            duration_of_stay=7,
            accommodation_address="Test Hotel, Test Street",
            travel_date=timezone.now().date() + timezone.timedelta(days=30)
        )
        
        self.stdout.write(f"✅ Created order {order.id} with BarbadosForm")
        self.stdout.write(f"   Customer: {order.first_name} {order.surname}")
        self.stdout.write(f"   Location: {barbados_location.location_name}")
        self.stdout.write(f"   Form data: Complete")
        
        # Create job manually
        job = QueuedJob.objects.create(
            order=order,
            location=barbados_location,
            status='queued',
            max_retries=3,
            retry_count=0
        )
        
        self.stdout.write(f"✅ Created job {job.id}")
        
        # Process the job directly to see the actual bot error
        self.stdout.write(f"\n🚀 PROCESSING JOB TO TRIGGER BOT ERROR...")
        self.stdout.write("-" * 40)
        
        try:
            result = process_order(str(order.id))
            self.stdout.write(f"Job result: {result}")
        except Exception as e:
            self.stdout.write(f"Job failed with exception: {str(e)}")
        
        # Check job status and errors
        job.refresh_from_db()
        self.stdout.write(f"\n📊 JOB STATUS AFTER PROCESSING:")
        self.stdout.write(f"   Status: {job.status}")
        self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
        self.stdout.write(f"   Failure Reason: {job.failure_reason}")
        self.stdout.write(f"   Error Message: {job.error_message}")
        
        # Check JobError records
        from queue_system.models import JobError
        errors = JobError.objects.filter(job=job)
        self.stdout.write(f"\n🔍 JOB ERRORS ({errors.count()}):")
        for i, error in enumerate(errors, 1):
            self.stdout.write(f"   Error {i}: {error.error_message}")
            if error.error_trace:
                self.stdout.write(f"   Trace: {error.error_trace[:200]}...")
        
        self.stdout.write(f"\n🌐 VIEW JOB DETAILS:")
        self.stdout.write(f"   http://localhost:8000/queue/admin/job-details/{job.id}/")
        self.stdout.write(f"   http://localhost:8000/queue/admin/review-queue/")
