from django.core.management.base import BaseCommand
from queue_system.models import <PERSON><PERSON><PERSON><PERSON>, JobError

class Command(BaseCommand):
    help = 'Check Job 56 error display'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔍 CHECKING JOB 56 (NEW JOB)"))
        self.stdout.write("=" * 50)
        
        try:
            job = QueuedJob.objects.get(id=56)
            
            self.stdout.write(f"📋 JOB 56 DETAILS:")
            self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
            self.stdout.write(f"   Status: {job.status}")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"   Error Message: {job.error_message}")
            self.stdout.write(f"   Failure Reason: {job.failure_reason}")
            self.stdout.write(f"   Created: {job.created_at}")
            
            # Check JobError records
            errors = JobError.objects.filter(job=job).order_by('-occurred_at')
            self.stdout.write(f"\n🔍 JOB ERROR RECORDS ({errors.count()}):")
            
            for i, error in enumerate(errors, 1):
                self.stdout.write(f"   Error {i}:")
                self.stdout.write(f"     Message: {error.error_message}")
                self.stdout.write(f"     Occurred: {error.occurred_at}")
                if error.error_trace:
                    trace_preview = error.error_trace[:100] + "..." if len(error.error_trace) > 100 else error.error_trace
                    self.stdout.write(f"     Trace Preview: {trace_preview}")
            
            # Analysis of error quality
            self.stdout.write(f"\n📊 ERROR ANALYSIS:")
            
            if job.error_message:
                if 'No Barbados form data found' in job.error_message:
                    self.stdout.write(f"   ✅ PERFECT: Shows specific missing data error")
                elif 'Element not found' in job.error_message:
                    self.stdout.write(f"   ✅ PERFECT: Shows specific Selenium error")
                elif 'Timeout' in job.error_message:
                    self.stdout.write(f"   ✅ PERFECT: Shows specific timeout error")
                elif 'Unable to select' in job.error_message:
                    self.stdout.write(f"   ✅ PERFECT: Shows specific dropdown error")
                elif 'Problem with' in job.error_message and 'database' in job.error_message:
                    self.stdout.write(f"   ✅ PERFECT: Shows specific validation error")
                elif job.error_message == 'Bot execution failed':
                    self.stdout.write(f"   ❌ GENERIC: Still showing generic error")
                elif 'Max retries reached' in job.error_message:
                    self.stdout.write(f"   ❌ WRAPPER: Still showing retry wrapper")
                elif 'Retry' in job.error_message and job.error_message.startswith('Retry'):
                    self.stdout.write(f"   ❌ WRAPPER: Still showing retry wrapper")
                else:
                    self.stdout.write(f"   ✅ SPECIFIC: Shows detailed error message")
                    self.stdout.write(f"       Length: {len(job.error_message)} characters")
            else:
                self.stdout.write(f"   ❌ NO ERROR: Error message is None")
            
            # Check failure reason
            if job.failure_reason:
                reason_map = {
                    'missing_form_data': '✅ Missing Form Data',
                    'bot_execution_error': '⚠️  Bot Execution Error (generic)',
                    'invalid_order_data': '✅ Invalid Order Data',
                    'network_error': '✅ Network Error',
                    'authentication_failed': '✅ Authentication Failed',
                    'website_structure_changed': '✅ Website Structure Changed',
                    'bot_configuration_error': '✅ Bot Configuration Error'
                }
                status = reason_map.get(job.failure_reason, f"❓ {job.failure_reason}")
                self.stdout.write(f"   Failure Reason: {status}")
            else:
                self.stdout.write(f"   ❌ NO REASON: Failure reason is None")
            
            # Compare with Job 47
            self.stdout.write(f"\n🔄 COMPARISON WITH JOB 47:")
            try:
                job47 = QueuedJob.objects.get(id=47)
                self.stdout.write(f"   Job 47 Error: {job47.error_message}")
                self.stdout.write(f"   Job 56 Error: {job.error_message}")
                
                if job.error_message and job47.error_message:
                    if len(job.error_message) > len(job47.error_message):
                        self.stdout.write(f"   ✅ Job 56 has MORE detailed error")
                    elif job.error_message != job47.error_message:
                        self.stdout.write(f"   ✅ Job 56 has DIFFERENT error (good)")
                    else:
                        self.stdout.write(f"   ⚠️  Job 56 has SAME error as Job 47")
            except:
                pass
            
            self.stdout.write(f"\n🌐 VIEW IN ADMIN:")
            self.stdout.write(f"   Job Details: http://localhost:8000/queue/admin/job-details/56/")
            self.stdout.write(f"   Review Queue: http://localhost:8000/queue/admin/review-queue/")
            
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR("Job 56 not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
