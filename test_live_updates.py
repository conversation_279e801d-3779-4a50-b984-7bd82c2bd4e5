#!/usr/bin/env python
"""
Test script for live updates and worker management functionality
"""

import requests
import json
import time

def test_api_endpoints():
    """Test all API endpoints"""
    
    print("🧪 TESTING LIVE UPDATE FUNCTIONALITY")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test 1: Overview API
    print("\n1. Testing Overview API...")
    try:
        response = requests.get(f"{base_url}/queue/api/live-stats/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                overview = data['overview']
                print(f"   ✅ Total Workers: {overview['total_workers']}/{overview['total_max_workers']}")
                print(f"   ✅ Total Queued: {overview['total_queued']}")
                print(f"   ✅ Locations: {len(overview['locations'])}")
            else:
                print(f"   ❌ API returned success=False")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Location-specific API
    print("\n2. Testing Location API...")
    try:
        # Get first location ID
        import os
        import sys
        import django
        
        # Setup Django
        sys.path.insert(0, '.')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
        django.setup()
        
        from locations.models import Location
        location = Location.objects.first()
        
        if location:
            response = requests.get(f"{base_url}/queue/api/live-stats/{location.id}/")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    loc_data = data['location']
                    print(f"   ✅ Location: {loc_data['name']}")
                    print(f"   ✅ Workers: {loc_data['active_workers']}/{loc_data['max_workers']}")
                    print(f"   ✅ Jobs - Queued: {loc_data['queued']}, Processing: {loc_data['processing']}")
                else:
                    print(f"   ❌ API returned success=False")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
        else:
            print("   ❌ No locations found")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Worker Adjustment (simulation)
    print("\n3. Testing Worker Adjustment API...")
    try:
        if location:
            # Test increase workers
            response = requests.post(f"{base_url}/queue/admin/adjust-workers/{location.id}/", 
                                   data={'action': 'increase'})
            print(f"   Increase Workers Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response: {data}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ API TESTING COMPLETE")
    print("\n💡 MANUAL TESTING:")
    print(f"   • Open: {base_url}/queue/admin/queue-overview/")
    print(f"   • Open: {base_url}/queue/admin/location/{location.id if location else 'LOCATION_ID'}/")
    print("   • Watch for live updates (every 10-15 seconds)")
    print("   • Test worker increase/decrease buttons")
    print("   • Check for real-time updates without page refresh")

if __name__ == '__main__':
    test_api_endpoints()
