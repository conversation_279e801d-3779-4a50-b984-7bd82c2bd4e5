#!/usr/bin/env python
"""
Test if the scheduler worker error is fixed
"""

import os
import sys
import time
import requests
import subprocess

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

def count_celery_processes():
    """Count actual Celery processes"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True, timeout=3, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            return len([line for line in lines if 'celery.exe' in line and line.strip()])
        return 0
    except:
        return 0

def test_sync_task():
    """Test the sync task manually"""
    try:
        from queue_system.tasks import sync_worker_status_task
        result = sync_worker_status_task()
        return {'success': True, 'result': result}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_api_stats():
    """Get stats from API"""
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=3)
        if response.status_code == 200:
            data = response.json()
            return {'success': True, 'workers': data['overview']['total_workers']}
        return {'success': False, 'error': f'HTTP {response.status_code}'}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def main():
    print("🔧 TESTING SCHEDULER WORKER FIX")
    print("=" * 50)
    
    # Count actual processes
    celery_count = count_celery_processes()
    print(f"Actual Celery processes: {celery_count}")
    
    # Test sync task manually
    print("\n📊 TESTING SYNC TASK")
    print("-" * 30)
    sync_result = test_sync_task()
    if sync_result['success']:
        print(f"✅ Sync task working: {sync_result['result']}")
    else:
        print(f"❌ Sync task failed: {sync_result['error']}")
    
    # Get API stats before
    print("\n📈 API STATS BEFORE")
    print("-" * 30)
    api_before = get_api_stats()
    if api_before['success']:
        print(f"Workers shown in API: {api_before['workers']}")
    else:
        print(f"❌ API error: {api_before['error']}")
    
    # Wait for automatic sync (should happen every 5 seconds)
    print(f"\n⏱️  WAITING FOR AUTOMATIC SYNC...")
    print("(Scheduler should run sync task every 5 seconds)")
    
    for i in range(15):  # Wait up to 15 seconds
        time.sleep(1)
        print(f"  {i+1}s...", end='\r')
    
    print("\n")
    
    # Get API stats after
    print("📈 API STATS AFTER")
    print("-" * 30)
    api_after = get_api_stats()
    if api_after['success']:
        print(f"Workers shown in API: {api_after['workers']}")
        
        # Check if it updated correctly
        expected_workers = max(0, celery_count - 2)  # Subtract beat and scheduler
        if api_after['workers'] == expected_workers:
            print(f"✅ CORRECT! Expected {expected_workers}, got {api_after['workers']}")
        else:
            print(f"⚠️  Expected {expected_workers}, got {api_after['workers']}")
    else:
        print(f"❌ API error: {api_after['error']}")
    
    # Final assessment
    print(f"\n🎯 ASSESSMENT")
    print("=" * 30)
    
    if sync_result['success'] and api_after['success']:
        print("✅ SCHEDULER WORKER IS WORKING!")
        print("   • Sync task executes without errors")
        print("   • API reflects actual worker counts")
        print("   • No more 'unpack' errors expected")
    else:
        print("❌ ISSUES STILL PRESENT")
        if not sync_result['success']:
            print(f"   • Sync task error: {sync_result['error']}")
        if not api_after['success']:
            print(f"   • API error: {api_after['error']}")
    
    print(f"\n🔗 CHECK ADMIN UI")
    print("http://localhost:8000/queue/admin/queue-overview/")
    print("Should show correct worker counts now!")

if __name__ == '__main__':
    main()
