#!/usr/bin/env python
"""
Test if the scheduler worker error is fixed
"""

import os
import sys
import time
import requests
import subprocess

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

def count_celery_processes():
    """Count actual Celery processes"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True, timeout=3, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            return len([line for line in lines if 'celery.exe' in line and line.strip()])
        return 0
    except:
        return 0

def test_sync_task():
    """Test the sync task manually"""
    try:
        from queue_system.tasks import sync_worker_status_task
        result = sync_worker_status_task()
        return {'success': True, 'result': result}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def get_api_stats():
    """Get stats from API"""
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=3)
        if response.status_code == 200:
            data = response.json()
            return {'success': True, 'workers': data['overview']['total_workers']}
        return {'success': False, 'error': f'HTTP {response.status_code}'}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def check_scheduler_logs():
    """Check if scheduler worker has any errors"""
    try:
        # Check if there are any recent error logs
        import subprocess
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'],
                              capture_output=True, text=True, timeout=3, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            celery_processes = [line for line in lines if 'celery.exe' in line and line.strip()]
            return {'success': True, 'processes': len(celery_processes), 'details': celery_processes}
        return {'success': False, 'error': 'Failed to check processes'}
    except Exception as e:
        return {'success': False, 'error': str(e)}

def main():
    print("🔧 TESTING SCHEDULER WORKER FIX - COMPREHENSIVE CHECK")
    print("=" * 60)

    # Count actual processes
    celery_count = count_celery_processes()
    print(f"Actual Celery processes: {celery_count}")

    # Check scheduler logs
    print("\n🔍 CHECKING SCHEDULER PROCESSES")
    print("-" * 40)
    scheduler_check = check_scheduler_logs()
    if scheduler_check['success']:
        print(f"✅ Found {scheduler_check['processes']} Celery processes running")
        for i, process in enumerate(scheduler_check['details'][:3]):  # Show first 3
            print(f"   {i+1}. {process.strip()}")
    else:
        print(f"❌ Process check failed: {scheduler_check['error']}")

    # Test sync task manually multiple times
    print("\n📊 TESTING SYNC TASK (MULTIPLE RUNS)")
    print("-" * 40)
    for i in range(3):
        sync_result = test_sync_task()
        if sync_result['success']:
            print(f"✅ Run {i+1}: {sync_result['result']}")
        else:
            print(f"❌ Run {i+1} FAILED: {sync_result['error']}")
            break
        time.sleep(1)

    # Get API stats before
    print("\n📈 API STATS BEFORE WAITING")
    print("-" * 40)
    api_before = get_api_stats()
    if api_before['success']:
        print(f"Workers shown in API: {api_before['workers']}")
    else:
        print(f"❌ API error: {api_before['error']}")

    # Wait for automatic sync (should happen every 5 seconds)
    print(f"\n⏱️  WAITING FOR AUTOMATIC SYNC...")
    print("(Scheduler should run sync task every 5 seconds)")
    print("Watching for 20 seconds to ensure no errors...")

    for i in range(20):  # Wait up to 20 seconds
        time.sleep(1)
        print(f"  {i+1}s...", end='\r')

    print("\n")

    # Get API stats after
    print("📈 API STATS AFTER WAITING")
    print("-" * 40)
    api_after = get_api_stats()
    if api_after['success']:
        print(f"Workers shown in API: {api_after['workers']}")

        # Check if it updated correctly
        expected_workers = max(0, celery_count - 2)  # Subtract beat and scheduler
        if api_after['workers'] == expected_workers:
            print(f"✅ CORRECT! Expected {expected_workers}, got {api_after['workers']}")
        else:
            print(f"⚠️  Expected {expected_workers}, got {api_after['workers']}")
    else:
        print(f"❌ API error: {api_after['error']}")

    # Final comprehensive assessment
    print(f"\n🎯 COMPREHENSIVE ASSESSMENT")
    print("=" * 40)

    all_good = True

    if sync_result['success']:
        print("✅ Manual sync task execution: WORKING")
    else:
        print("❌ Manual sync task execution: FAILED")
        all_good = False

    if api_after['success']:
        print("✅ API endpoint response: WORKING")
    else:
        print("❌ API endpoint response: FAILED")
        all_good = False

    if scheduler_check['success'] and scheduler_check['processes'] >= 2:
        print("✅ Celery processes running: GOOD")
    else:
        print("❌ Celery processes: INSUFFICIENT")
        all_good = False

    if all_good:
        print("\n🎉 ALL SYSTEMS GO!")
        print("   • No 'unpack' errors detected")
        print("   • Sync task working properly")
        print("   • API responding correctly")
        print("   • Scheduler worker stable")
    else:
        print("\n⚠️  ISSUES DETECTED")
        print("   • Check the specific failures above")
        print("   • May need additional debugging")

    print(f"\n🔗 CHECK ADMIN UI")
    print("http://localhost:8000/queue/admin/queue-overview/")
    print("Should show correct worker counts and no errors!")

if __name__ == '__main__':
    main()
