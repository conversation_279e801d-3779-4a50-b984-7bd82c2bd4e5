#!/usr/bin/env python
"""
Automatic Job Processor
Continuously polls for queued jobs and processes them.
Run this alongside your Celery workers for automatic job processing.
"""

import os
import sys
import django
import time
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob
from queue_system.tasks import process_order

def log(message):
    """Print message with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}", flush=True)

def process_queued_jobs():
    """Process all queued jobs"""
    jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
    
    if not jobs.exists():
        return 0
    
    processed = 0
    for job in jobs[:3]:  # Process max 3 jobs at a time
        log(f"Processing job {job.id} for order {job.order.id} ({job.order.first_name} {job.order.surname})")
        
        try:
            result = process_order(str(job.order.id))
            if result:
                log(f"✓ Job {job.id} completed successfully")
            else:
                log(f"✗ Job {job.id} failed")
            processed += 1
        except Exception as e:
            log(f"✗ Error processing job {job.id}: {str(e)}")
            processed += 1
    
    return processed

def main():
    log("Starting Automatic Job Processor")
    log("This will continuously check for queued jobs and process them")
    log("Press Ctrl+C to stop")
    log("=" * 60)
    
    cycle = 0
    try:
        while True:
            cycle += 1
            log(f"Cycle {cycle}: Checking for queued jobs...")
            
            processed = process_queued_jobs()
            
            if processed > 0:
                log(f"Processed {processed} jobs")
            else:
                log("No queued jobs found")
            
            log("Waiting 20 seconds...")
            time.sleep(20)
            
    except KeyboardInterrupt:
        log("Automatic Job Processor stopped by user")
    except Exception as e:
        log(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
