import subprocess
from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig, Location

class Command(BaseCommand):
    help = 'Start a Celery worker for a specific location'

    def add_arguments(self, parser):
        parser.add_argument('location_id', type=str, help='ID of the location to start a worker for')
        
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Run in debug mode with more verbose output',
        )

    def handle(self, *args, **options):
        location_id = options['location_id']
        
        try:
            # Get the location
            location = Location.objects.get(id=location_id)
            self.stdout.write(f"Starting worker for location: {location.location_name}")
            
            # Get or create the location config
            config, created = LocationQueueConfig.objects.get_or_create(
                location=location,
                defaults={
                    'has_time_window': False,
                    'max_workers': 1,
                    'active_workers': 0,
                    'priority_level': 1
                }
            )
            
            # Build the Celery worker command
            cmd = [
                'celery', 
                '-A', 'config', 
                'worker',
                '-Q', f'location.{location_id}',
                '-c', '1',
                '--loglevel=info' if not options['debug'] else '--loglevel=debug',
                '-n', f'worker.{location.location_name.replace(" ", "_")}@%h'
            ]
            
            self.stdout.write(self.style.SUCCESS(
                f"Starting worker for location: {location.location_name} (Queue: location.{location_id})"
            ))
            
            # Start the worker process in the foreground
            self.stdout.write("Starting worker in foreground mode. Press Ctrl+C to stop.")
            subprocess.run(cmd)
            
        except Location.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Location {location_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error starting worker: {str(e)}"))