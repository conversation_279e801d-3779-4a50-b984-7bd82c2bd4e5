asgiref==3.8.1
attrs==25.3.0
beautifulsoup4==4.13.4
cachetools==5.5.2
certifi==2025.4.26
celery==5.2.7
billiard==3.6.4.0
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
config==0.5.1
curl_cffi==0.10.0
Django<5.0
django-celery-beat==2.5.0
django-celery-results==2.5.1
django-cors-headers==4.7.0
djangorestframework==3.16.0
exceptiongroup==1.2.2
frozendict==2.4.6
google-auth==2.39.0
google-auth-oauthlib==1.2.2
gspread==6.2.0
h11==0.16.0
httplib2==0.22.0
idna==3.10
kombu==5.3.5
lxml==5.4.0
multitasking==0.0.11
mysqlclient==2.2.7
nsepy==0.8
numpy<2.2.6
oauth2client==4.1.3
oauthlib==3.2.2
outcome==1.3.0.post0
packaging==25.0
pandas==2.2.3
peewee==3.18.1
platformdirs==4.3.7
protobuf==5.29.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pyparsing==3.2.3
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
redis==5.0.1
requests==2.32.3
requests-oauthlib==2.0.0
rsa==4.9.1
selenium==4.31.0
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.7
sqlparse==0.5.3
trio==0.30.0
trio-websocket==0.12.2
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
vine==5.1.0
webdriver-manager==4.0.2
websocket-client==1.8.0
websockets==15.0.1
wsproto==1.2.0
yfinance==0.2.59
django-mathfilters==1.0.0
psutil==7.0.0
