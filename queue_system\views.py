from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import timedelta, datetime
from .models import QueuedJob, LocationQueueConfig, JobError
from locations.models import Location
import json

@staff_member_required
def queue_overview(request):
    """Queue Overview Dashboard - Shows active queue length, workers, success rates per location"""
    
    # Get all locations with their queue configs
    locations = Location.objects.all()
    location_stats = []
    
    total_active = 0
    total_waiting = 0
    total_workers = 0
    total_max_workers = 0
    
    for location in locations:
        # Get or create queue config
        config, created = LocationQueueConfig.objects.get_or_create(
            location=location,
            defaults={'max_workers': 1, 'active_workers': 0}
        )
        
        # Active queue (queued + processing + requeued)
        active_count = QueuedJob.objects.filter(
            location=location,
            status__in=['queued', 'processing', 'requeued']
        ).count()
        
        # Waiting queue (jobs scheduled for future)
        waiting_count = QueuedJob.objects.filter(
            location=location,
            status='queued',
            scheduled_for__gt=timezone.now()
        ).count()
        
        # Success/failure rates (last 7 days)
        week_ago = timezone.now() - timedelta(days=7)
        completed_jobs = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=week_ago,
            status='completed'
        ).count()
        
        failed_jobs = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=week_ago,
            status__in=['failed', 'review']
        ).count()
        
        total_jobs = completed_jobs + failed_jobs
        success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
        
        # Recent completions/failures (last 24 hours)
        day_ago = timezone.now() - timedelta(hours=24)
        recent_completed = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=day_ago,
            status='completed'
        ).count()
        
        recent_failed = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=day_ago,
            status__in=['failed', 'review']
        ).count()
        
        location_stats.append({
            'location': location,
            'config': config,
            'active_queue_length': active_count,
            'waiting_queue_length': waiting_count,
            'active_workers': config.active_workers,
            'max_workers': config.max_workers,
            'success_rate': round(success_rate, 1),
            'recent_completed': recent_completed,
            'recent_failed': recent_failed,
            'total_jobs_week': total_jobs
        })
        
        total_active += active_count
        total_waiting += waiting_count
        total_workers += config.active_workers
        total_max_workers += config.max_workers
    
    # Overall system stats
    overall_stats = {
        'total_active_jobs': total_active,
        'total_waiting_jobs': total_waiting,
        'total_active_workers': total_workers,
        'total_max_workers': total_max_workers,
        'worker_utilization': round((total_workers / total_max_workers * 100) if total_max_workers > 0 else 0, 1)
    }
    
    context = {
        'location_stats': location_stats,
        'overall_stats': overall_stats,
        'title': 'Queue Overview Dashboard'
    }
    
    return render(request, 'admin/queue_system/queue_overview.html', context)

@staff_member_required
def location_queue_details(request, location_id):
    """Location Queue Details - Shows jobs in active/waiting queues with worker controls"""
    
    location = get_object_or_404(Location, id=location_id)
    config, created = LocationQueueConfig.objects.get_or_create(
        location=location,
        defaults={'max_workers': 1, 'active_workers': 0}
    )
    
    # Active queue jobs (queued, processing, requeued)
    active_jobs = QueuedJob.objects.filter(
        location=location,
        status__in=['queued', 'processing', 'requeued']
    ).order_by('-priority_flag', 'created_at')
    
    # Waiting queue jobs (scheduled for future)
    waiting_jobs = QueuedJob.objects.filter(
        location=location,
        status='queued',
        scheduled_for__gt=timezone.now()
    ).order_by('scheduled_for')
    
    # Add age calculation for active jobs
    now = timezone.now()
    for job in active_jobs:
        if job.created_at:
            age = now - job.created_at
            job.age_hours = age.total_seconds() / 3600
    
    context = {
        'location': location,
        'config': config,
        'active_jobs': active_jobs,
        'waiting_jobs': waiting_jobs,
        'title': f'Queue Details - {location.location_name}'
    }
    
    return render(request, 'admin/queue_system/location_queue_details.html', context)

@staff_member_required
def review_queue_dashboard(request):
    """Review Queue Dashboard - Shows failed jobs across all locations with filtering"""
    
    # Get filter parameters
    location_filter = request.GET.get('location')
    error_type_filter = request.GET.get('error_type')
    
    # Base queryset for review queue - include both failed and review status jobs
    review_jobs = QueuedJob.objects.filter(
        status__in=['review', 'failed']
    ).select_related('order', 'location')

    # Apply filters
    if location_filter:
        review_jobs = review_jobs.filter(location_id=location_filter)

    if error_type_filter:
        review_jobs = review_jobs.filter(failure_reason=error_type_filter)

    review_jobs = review_jobs.order_by('-created_at')
    
    # Get filter options
    locations = Location.objects.all()
    error_types = QueuedJob.objects.filter(
        failure_reason__isnull=False
    ).values_list('failure_reason', flat=True).distinct()
    
    # Statistics
    total_review_jobs = QueuedJob.objects.filter(status__in=['review', 'failed']).count()
    reviewed_jobs = QueuedJob.objects.filter(
        status__in=['review', 'failed'],
        reviewed_by__isnull=False
    ).count()
    
    context = {
        'review_jobs': review_jobs,
        'locations': locations,
        'error_types': error_types,
        'selected_location': location_filter,
        'selected_error_type': error_type_filter,
        'total_review_jobs': total_review_jobs,
        'reviewed_jobs': reviewed_jobs,
        'title': 'Review Queue Dashboard'
    }
    
    return render(request, 'admin/queue_system/review_queue_dashboard.html', context)

@staff_member_required
def job_details(request, job_id):
    """Job Details - Complete job history, errors, order data, manual intervention"""
    
    job = get_object_or_404(QueuedJob, id=job_id)
    
    # Get all errors for this job
    job_errors = JobError.objects.filter(job=job).order_by('-occurred_at')

    # Group errors by message to show retry information
    error_groups = {}
    for error in job_errors:
        msg = error.error_message
        if msg not in error_groups:
            error_groups[msg] = []
        error_groups[msg].append(error)

    # Get the actual bot error message (not retry wrapper)
    actual_error_message = None
    if job_errors.exists():
        # Look for the original error message (not retry messages)
        for error in job_errors:
            if not error.error_message.startswith('Retry ') and not error.error_message.startswith('Max retries'):
                actual_error_message = error.error_message
                break

        # If no original found, use the latest error
        if not actual_error_message:
            actual_error_message = job_errors.first().error_message

    context = {
        'job': job,
        'job_errors': job_errors,
        'error_groups': error_groups,
        'actual_error_message': actual_error_message,
        'order': job.order,
        'location': job.location,
        'title': f'Job Details - {job.id}'
    }
    
    return render(request, 'admin/queue_system/job_details.html', context)

@staff_member_required
def adjust_workers(request, location_id):
    """AJAX endpoint to adjust worker count for a location"""
    
    if request.method == 'POST':
        location = get_object_or_404(Location, id=location_id)
        config, created = LocationQueueConfig.objects.get_or_create(
            location=location,
            defaults={'max_workers': 1, 'active_workers': 0}
        )
        
        action = request.POST.get('action')
        
        if action == 'increase' and config.active_workers < config.max_workers:
            config.active_workers += 1
            config.save()
            messages.success(request, f'Increased workers for {location.location_name} to {config.active_workers}')
        elif action == 'decrease' and config.active_workers > 0:
            config.active_workers -= 1
            config.save()
            messages.success(request, f'Decreased workers for {location.location_name} to {config.active_workers}')
        elif action == 'set_max':
            new_max = int(request.POST.get('max_workers', 1))
            config.max_workers = max(1, new_max)
            config.active_workers = min(config.active_workers, config.max_workers)
            config.save()
            messages.success(request, f'Set max workers for {location.location_name} to {config.max_workers}')
        
        return JsonResponse({
            'success': True,
            'active_workers': config.active_workers,
            'max_workers': config.max_workers
        })
    
    return JsonResponse({'success': False})

@staff_member_required
def job_action(request, job_id):
    """Handle manual job interventions (requeue, prioritize, cancel)"""
    
    if request.method == 'POST':
        job = get_object_or_404(QueuedJob, id=job_id)
        action = request.POST.get('action')
        
        if action == 'requeue':
            priority = request.POST.get('priority') == 'true'
            reason = request.POST.get('reason', 'Manual requeue via admin')
            
            job.requeue_job(
                admin_user=request.user.username,
                priority=priority,
                reason=reason
            )
            messages.success(request, f'Job {job_id} requeued successfully')
            
        elif action == 'prioritize':
            job.priority_flag = True
            job.save()
            messages.success(request, f'Job {job_id} marked as priority')
            
        elif action == 'cancel':
            job.status = 'cancelled'
            job.save()
            messages.success(request, f'Job {job_id} cancelled')
            
        elif action == 'move_to_review':
            notes = request.POST.get('notes', 'Moved to review via admin')
            job.move_to_review(
                admin_user=request.user.username,
                notes=notes
            )
            messages.success(request, f'Job {job_id} moved to review queue')
    
    return redirect('admin:queue_system_queuedjob_change', job_id)
