#!/usr/bin/env python
import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob
from queue_system.tasks import process_order

def main():
    print("Starting simple automatic worker...")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    cycle = 0
    try:
        while True:
            cycle += 1
            print(f"\n[Cycle {cycle}] Checking for queued jobs...")
            
            # Get queued jobs
            jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
            
            if jobs.exists():
                job = jobs.first()
                print(f"  → Processing job {job.id} for order {job.order.id}")
                print(f"    Customer: {job.order.first_name} {job.order.surname}")
                
                try:
                    result = process_order(str(job.order.id))
                    if result:
                        print(f"    ✓ Job {job.id} completed successfully")
                    else:
                        print(f"    ✗ Job {job.id} failed")
                except Exception as e:
                    print(f"    ✗ Error: {str(e)}")
            else:
                print("  • No queued jobs found")
            
            print("  Waiting 15 seconds...")
            time.sleep(15)
            
    except KeyboardInterrupt:
        print("\n\nWorker stopped by user")

if __name__ == "__main__":
    main()
