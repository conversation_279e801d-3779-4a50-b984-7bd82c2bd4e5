from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from django.utils import timezone
from django.db import models
import json

class Command(BaseCommand):
    help = 'Demonstrate the enhanced admin features for failure handling'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🎯 DEMONSTRATING ENHANCED ADMIN FEATURES"))
        self.stdout.write("=" * 60)
        
        # Show current job statistics
        self.show_job_statistics()
        
        # Demonstrate failure handling
        self.demonstrate_failure_handling()
        
        # Show admin features
        self.show_admin_features()

    def show_job_statistics(self):
        self.stdout.write(f"\n📊 CURRENT JOB STATISTICS")
        self.stdout.write("-" * 40)
        
        stats = {
            'queued': QueuedJob.objects.filter(status='queued').count(),
            'processing': QueuedJob.objects.filter(status='processing').count(),
            'completed': QueuedJob.objects.filter(status='completed').count(),
            'failed': QueuedJob.objects.filter(status='failed').count(),
            'review': QueuedJob.objects.filter(status='review').count(),
            'requeued': QueuedJob.objects.filter(status='requeued').count(),
        }
        
        for status, count in stats.items():
            icon = {
                'queued': '⏳',
                'processing': '🔄',
                'completed': '✅',
                'failed': '❌',
                'review': '🔍',
                'requeued': '🔄'
            }.get(status, '❓')
            
            self.stdout.write(f"{icon} {status.title()}: {count}")
        
        # Show failure reasons
        failure_reasons = QueuedJob.objects.filter(failure_reason__isnull=False) \
            .values('failure_reason') \
            .annotate(count=models.Count('id')) \
            .order_by('-count')
        
        if failure_reasons:
            self.stdout.write(f"\n🚨 FAILURE REASONS:")
            for reason in failure_reasons:
                self.stdout.write(f"  • {reason['failure_reason']}: {reason['count']}")

    def demonstrate_failure_handling(self):
        self.stdout.write(f"\n🔧 FAILURE HANDLING DEMONSTRATION")
        self.stdout.write("-" * 40)
        
        # Find a failed job to demonstrate with
        failed_job = QueuedJob.objects.filter(status='failed').first()
        
        if failed_job:
            self.stdout.write(f"📋 Demonstrating with Job {failed_job.id}")
            self.stdout.write(f"   Customer: {failed_job.order.first_name} {failed_job.order.surname}")
            self.stdout.write(f"   Status: {failed_job.status}")
            self.stdout.write(f"   Retries: {failed_job.retry_count}/{failed_job.max_retries}")
            self.stdout.write(f"   Failure Reason: {failed_job.failure_reason or 'Unknown'}")
            
            # Demonstrate moving to review queue
            if failed_job.status == 'failed':
                self.stdout.write(f"\n🔍 Moving Job {failed_job.id} to review queue...")
                failed_job.move_to_review(
                    admin_user="demo_admin",
                    notes="Demonstrating admin review functionality"
                )
                self.stdout.write(f"   ✅ Job moved to review queue")
                
                # Show updated status
                failed_job.refresh_from_db()
                self.stdout.write(f"   📊 New Status: {failed_job.status}")
                self.stdout.write(f"   👤 Reviewed by: {failed_job.reviewed_by}")
                self.stdout.write(f"   📝 Notes: {failed_job.review_notes}")
        else:
            self.stdout.write("ℹ️ No failed jobs found to demonstrate with")

    def show_admin_features(self):
        self.stdout.write(f"\n🎛️ ENHANCED ADMIN FEATURES")
        self.stdout.write("-" * 40)
        
        self.stdout.write("✨ NEW ADMIN FEATURES AVAILABLE:")
        self.stdout.write("  📊 Enhanced Dashboard with Statistics")
        self.stdout.write("  🔍 Review Queue Management")
        self.stdout.write("  📈 Retry Count Tracking")
        self.stdout.write("  🚨 Error Details and Categorization")
        self.stdout.write("  🎯 Smart Filtering Options")
        self.stdout.write("  ⚡ Bulk Actions for Job Management")
        
        self.stdout.write(f"\n🎯 ADMIN FILTERS AVAILABLE:")
        self.stdout.write("  🔍 Review Queue Status")
        self.stdout.write("    • Needs Review")
        self.stdout.write("    • Under Review")
        self.stdout.write("    • High Retry Count")
        self.stdout.write("    • Critical Errors")
        self.stdout.write("    • Ready to Requeue")
        
        self.stdout.write("  📋 Failure Categories")
        self.stdout.write("    • Data Issues")
        self.stdout.write("    • Bot Issues")
        self.stdout.write("    • Website Issues")
        self.stdout.write("    • System Issues")
        
        self.stdout.write("  🔄 Retry Status")
        self.stdout.write("    • First Attempt")
        self.stdout.write("    • Retrying")
        self.stdout.write("    • Max Retries Reached")
        
        self.stdout.write(f"\n⚡ ADMIN ACTIONS AVAILABLE:")
        self.stdout.write("  • Mark as Priority")
        self.stdout.write("  • Move to Review Queue")
        self.stdout.write("  • Requeue from Review")
        self.stdout.write("  • Requeue Failed Jobs")
        self.stdout.write("  • Cancel Jobs")
        
        self.stdout.write(f"\n📋 ADMIN DISPLAY FEATURES:")
        self.stdout.write("  • Status with Icons (⏳🔄✅❌🔍)")
        self.stdout.write("  • Retry Count Display (X/3)")
        self.stdout.write("  • Failure Reason Categorization")
        self.stdout.write("  • Error Details (JSON formatted)")
        self.stdout.write("  • Review Information")
        self.stdout.write("  • Highlighted Review Queue Jobs")
        
        self.stdout.write(f"\n🌐 ACCESS THE ADMIN:")
        self.stdout.write("  1. Start Django server: python manage.py runserver")
        self.stdout.write("  2. Go to: http://localhost:8000/admin/")
        self.stdout.write("  3. Navigate to: Queue System > Queued Jobs")
        self.stdout.write("  4. Explore the enhanced interface!")
        
        self.stdout.write(f"\n" + "=" * 60)
        self.stdout.write(self.style.SUCCESS("🎉 ENHANCED ADMIN INTERFACE IS READY!"))
        self.stdout.write("All failure handling features are now available in the Django admin panel.")
