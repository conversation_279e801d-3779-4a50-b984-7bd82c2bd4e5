@echo off
echo 🔧 WORKER MANAGEMENT MENU
echo =========================
echo 1. Start Production Workers
echo 2. Check Worker Status  
echo 3. Sync Worker Status (Database)
echo 4. Stop All Workers
echo 5. View Admin UI
echo =========================

set /p choice="Enter choice (1-5): "

if "%choice%"=="1" (
    echo Starting production workers...
    python manage.py start_production_workers --workers-per-location=1 --concurrency=1 --beat
) else if "%choice%"=="2" (
    echo Checking worker status...
    python manage.py check_workers
) else if "%choice%"=="3" (
    echo Syncing worker status with database...
    python manage.py sync_worker_status
) else if "%choice%"=="4" (
    echo Stopping all Celery workers...
    taskkill /F /IM celery.exe
    echo All workers stopped
) else if "%choice%"=="5" (
    echo Opening admin UI...
    start http://localhost:8000/queue/admin/queue-overview/
) else (
    echo Invalid choice
)

echo.
pause
