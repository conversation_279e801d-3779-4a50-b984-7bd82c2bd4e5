from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time
import redis
from django.conf import settings

class Command(BaseCommand):
    help = 'Start hybrid workers: Redis workers + fallback processing'

    def check_redis_connection(self):
        """Check if Redis is available"""
        try:
            # Try to connect to Redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            return True
        except Exception as e:
            self.stdout.write(f"  ❌ Redis connection failed: {str(e)}")
            return False

    def add_arguments(self, parser):
        parser.add_argument('--location', type=str, help='Start workers for specific location only')
        parser.add_argument('--concurrency', type=int, default=1, help='Number of worker processes per location')
        parser.add_argument('--beat', action='store_true', help='Start Celery beat scheduler')
        parser.add_argument('--redis-only', action='store_true', help='Only start Redis workers (no fallback)')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING HYBRID QUEUE WORKERS"))
        self.stdout.write("=" * 60)
        self.stdout.write("This system uses Redis workers with direct processing fallback")

        # Check Redis connection first
        self.stdout.write(f"\n🔍 CHECKING SYSTEM REQUIREMENTS...")
        redis_available = self.check_redis_connection()

        if not redis_available:
            self.stdout.write(self.style.WARNING("⚠️  Redis not available - workers will use fallback mode only"))
        else:
            self.stdout.write(self.style.SUCCESS("✅ Redis connection successful"))
        
        # Get locations
        if options['location']:
            locations = Location.objects.filter(location_name__icontains=options['location'])
        else:
            locations = Location.objects.all()
        
        if not locations.exists():
            self.stdout.write(self.style.ERROR("No locations found"))
            return
        
        concurrency = options['concurrency']
        workers_started = []
        
        # Start Redis workers for each location (only if Redis is available)
        if redis_available:
            self.stdout.write(f"\n🚀 Starting Redis workers for {locations.count()} locations...")

            for location in locations:
                queue_name = f'location.{location.id}'
                worker_name = f'redis_worker_{location.location_name}_{location.id}'

                self.stdout.write(f"  → Starting Redis worker for {location.location_name}")
                self.stdout.write(f"    Queue: {queue_name}")

                # Build the celery worker command
                cmd = [
                    'celery', '-A', 'config', 'worker',
                    '--loglevel=info',
                    f'--concurrency={concurrency}',
                    f'--queues={queue_name}',
                    f'--hostname={worker_name}@%h',
                    '--without-gossip',
                    '--without-mingle',
                    '--without-heartbeat'
                ]

                try:
                    # Start the worker process
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )

                    workers_started.append({
                        'type': 'redis',
                        'location': location.location_name,
                        'queue': queue_name,
                        'pid': process.pid,
                        'process': process
                    })

                    self.stdout.write(f"    ✓ Redis worker started (PID: {process.pid})")

                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"    ⚠ Redis worker failed: {e}"))
                    self.stdout.write(f"    → Jobs will use direct processing fallback")
        else:
            self.stdout.write(f"\n⚠️  SKIPPING REDIS WORKERS (Redis not available)")
            self.stdout.write(f"   → All jobs will use direct processing fallback")

        # Start Celery Beat if requested (only if Redis is available)
        if options['beat'] and redis_available:
            self.stdout.write(f"\n📅 Starting Celery Beat scheduler...")
        elif options['beat'] and not redis_available:
            self.stdout.write(f"\n⚠️  SKIPPING CELERY BEAT (Redis not available)")
            self.stdout.write(f"   → Scheduled tasks will not work without Redis")
            
            beat_cmd = [
                'celery', '-A', 'config', 'beat',
                '--loglevel=info',
                '--scheduler=django_celery_beat.schedulers:DatabaseScheduler'
            ]
            
            try:
                beat_process = subprocess.Popen(
                    beat_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'type': 'scheduler',
                    'location': 'scheduler',
                    'queue': 'beat',
                    'pid': beat_process.pid,
                    'process': beat_process
                })
                
                self.stdout.write(f"  ✓ Celery Beat started (PID: {beat_process.pid})")
                
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"  ⚠ Celery Beat failed: {e}"))
        
        # Start fallback processor if not Redis-only
        if not options['redis_only']:
            self.stdout.write(f"\n🔄 Starting fallback job processor...")
            
            fallback_cmd = [
                'python', 'manage.py', 'auto_worker',
                '--interval=30',
                '--max-jobs-per-cycle=5'
            ]
            
            try:
                fallback_process = subprocess.Popen(
                    fallback_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'type': 'fallback',
                    'location': 'all',
                    'queue': 'database',
                    'pid': fallback_process.pid,
                    'process': fallback_process
                })
                
                self.stdout.write(f"  ✓ Fallback processor started (PID: {fallback_process.pid})")
                
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"  ⚠ Fallback processor failed: {e}"))
        
        # Summary
        self.stdout.write(f"\n" + "="*60)
        self.stdout.write(self.style.SUCCESS(f"🎉 HYBRID QUEUE SYSTEM STARTED"))
        self.stdout.write(f"Started {len(workers_started)} workers:")
        
        for worker in workers_started:
            icon = "🔴" if worker['type'] == 'redis' else "📅" if worker['type'] == 'scheduler' else "🔄"
            self.stdout.write(f"  {icon} {worker['type'].title()}: {worker['location']} (PID: {worker['pid']})")
        
        self.stdout.write(f"\n📋 How it works:")
        self.stdout.write(f"  1. Jobs are sent to Redis queues first")
        self.stdout.write(f"  2. If Redis fails, jobs are processed directly")
        self.stdout.write(f"  3. Fallback processor handles any missed jobs")
        
        self.stdout.write(f"\n🔍 Monitoring:")
        self.stdout.write(f"  • Check jobs: python manage.py test_queue --list-jobs")
        self.stdout.write(f"  • Check processes: tasklist /FI \"IMAGENAME eq python.exe\"")
        self.stdout.write(f"  • Test system: python manage.py test_queue --create-test-order")
        
        self.stdout.write("="*60)
