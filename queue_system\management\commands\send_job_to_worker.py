from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
from orders.models import order
import time

class Command(BaseCommand):
    help = 'Send a job to workers and monitor if they pick it up'

    def add_arguments(self, parser):
        parser.add_argument('--order-id', type=str, help='Order ID to process')
        parser.add_argument('--monitor', action='store_true', help='Monitor job status changes')

    def handle(self, *args, **options):
        if options['order_id']:
            order_id = options['order_id']
        else:
            # Get a queued job
            job = QueuedJob.objects.filter(status='queued').first()
            if not job:
                self.stdout.write(self.style.ERROR("No queued jobs found"))
                return
            order_id = str(job.order.id)

        self.stdout.write(f"Sending job for order {order_id} to workers...")
        
        # Try to send the task using delay (which should work with database broker)
        try:
            task = process_order.delay(order_id)
            self.stdout.write(f"Task sent with ID: {task.id}")
            
            if options['monitor']:
                self.monitor_job_status(order_id)
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Failed to send task: {str(e)}"))
            import traceback
            self.stdout.write(traceback.format_exc())

    def monitor_job_status(self, order_id):
        self.stdout.write("Monitoring job status (press Ctrl+C to stop)...")
        
        try:
            while True:
                job = QueuedJob.objects.filter(order_id=order_id).first()
                if job:
                    self.stdout.write(f"Job {job.id} status: {job.status}")
                    if job.status in ['completed', 'failed']:
                        self.stdout.write(f"Job finished with status: {job.status}")
                        break
                else:
                    self.stdout.write("No job found for this order")
                    break
                    
                time.sleep(2)
                
        except KeyboardInterrupt:
            self.stdout.write("\nMonitoring stopped by user")
