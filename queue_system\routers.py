import logging

logger = logging.getLogger(__name__)

def route_process_order(name, args, kwargs, options, task=None, **kw):
    """Route process_order tasks to location-specific queues"""
    # task_name = getattr(task, 'name', name)

    logger.info("running routers")
    
    # Only route our specific task
    if name not in ['queue_system.tasks.process_order', 'config.tasks.process_order']:
        return None  # Let other tasks use default routing

    try:
        # Get order_id from either args or kwargs
        order_id = args[0] if args else kwargs.get('order_id')
        logger.info(f'[Router] order_id =  {order_id}')
        if not order_id:
            logger.warning("[Router] No order_id provided")
            return {'queue': 'default'}

        import uuid
        # Convert string UUID if needed
        if isinstance(order_id, str):
            try:
                order_id = uuid.UUID(order_id)
            except ValueError:
                logger.warning(f"[Router] Invalid UUID: {order_id}")
                return {'queue': 'default'}
            
        from queue_system.models import QueuedJob
        from orders.models import order as Order

        # Try to find the job with location
        job = (QueuedJob.objects
               .filter(order_id=order_id)
               .first())
        
        
        if job and job.location.id:
            return {'queue': f'location.{job.location.id}'}

        # Fallback to order's location
        # order = (Order.objects
        #         .filter(id=order_id)
        #         .first())
        
        logger.info(f'[Router] end')
        # if order and order.location:
        #     return {'queue': f'location.{order.location.id}'}

        # logger.warning(f"[Router] No location found for order {order_id}")
        return {'queue': 'default'}

    except Exception as e:
        logger.exception(f"[Router] Error: {e}")
        return {'queue': 'default'}