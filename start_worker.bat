@echo off
echo 🚀 STARTING PERSISTENT WORKER FOR BARBADOS
echo ============================================
echo This worker will keep running continuously
echo Press Ctrl+C to stop
echo ============================================

:start
echo [%time%] Starting worker...
python manage.py auto_worker --interval=30 --max-jobs-per-cycle=5 --location=Barbados --worker-name=persistent_worker

echo [%time%] Worker stopped - restarting in 5 seconds...
timeout /t 5 /nobreak >nul
goto start
