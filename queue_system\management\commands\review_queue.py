from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from django.utils import timezone
import json

class Command(BaseCommand):
    help = 'Manage the review queue for failed jobs'

    def add_arguments(self, parser):
        parser.add_argument('--list', action='store_true', help='List all jobs in review queue')
        parser.add_argument('--details', type=int, help='Show detailed information for a specific job ID')
        parser.add_argument('--requeue', type=int, help='Requeue a specific job ID')
        parser.add_argument('--priority', action='store_true', help='Requeue with high priority')
        parser.add_argument('--admin', type=str, default='system', help='Admin username for actions')
        parser.add_argument('--reason', type=str, help='Reason for requeuing')
        parser.add_argument('--notes', type=str, help='Review notes')
        parser.add_argument('--move-to-review', type=int, help='Move a failed job to review queue')

    def handle(self, *args, **options):
        if options['list']:
            self.list_review_jobs()
        elif options['details']:
            self.show_job_details(options['details'])
        elif options['requeue']:
            self.requeue_job(
                options['requeue'], 
                options['admin'], 
                options['priority'], 
                options['reason']
            )
        elif options['move_to_review']:
            self.move_to_review(
                options['move_to_review'],
                options['admin'],
                options['notes']
            )
        else:
            self.stdout.write(self.style.ERROR("Please specify an action: --list, --details, --requeue, or --move-to-review"))

    def list_review_jobs(self):
        """List all jobs in the review queue"""
        review_jobs = QueuedJob.objects.filter(status='review').order_by('-created_at')
        
        if not review_jobs.exists():
            self.stdout.write(self.style.SUCCESS("✓ No jobs in review queue"))
            return
        
        self.stdout.write(f"\n📋 REVIEW QUEUE ({review_jobs.count()} jobs)")
        self.stdout.write("=" * 80)
        
        for job in review_jobs:
            # Format dates
            created = job.created_at.strftime("%Y-%m-%d %H:%M")
            failed_at = job.completed_at.strftime("%Y-%m-%d %H:%M") if job.completed_at else "N/A"
            
            # Get customer info
            customer = f"{job.order.first_name} {job.order.surname}"
            
            self.stdout.write(f"\n🔍 Job {job.id}")
            self.stdout.write(f"   Customer: {customer}")
            self.stdout.write(f"   Location: {job.location.location_name}")
            self.stdout.write(f"   Created: {created}")
            self.stdout.write(f"   Failed: {failed_at}")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"   Reason: {job.failure_reason or 'Unknown'}")
            
            if job.error_message:
                error_preview = job.error_message[:100] + "..." if len(job.error_message) > 100 else job.error_message
                self.stdout.write(f"   Error: {error_preview}")
            
            if job.reviewed_by:
                reviewed = job.reviewed_at.strftime("%Y-%m-%d %H:%M") if job.reviewed_at else "N/A"
                self.stdout.write(f"   Reviewed by: {job.reviewed_by} at {reviewed}")
        
        self.stdout.write("\n" + "=" * 80)
        self.stdout.write("💡 Use --details <job_id> for full details")
        self.stdout.write("💡 Use --requeue <job_id> to requeue a job")

    def show_job_details(self, job_id):
        """Show detailed information for a specific job"""
        try:
            job = QueuedJob.objects.get(id=job_id)
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
            return
        
        self.stdout.write(f"\n📋 JOB {job.id} DETAILS")
        self.stdout.write("=" * 60)
        
        # Basic info
        self.stdout.write(f"Status: {job.get_status_display()}")
        self.stdout.write(f"Customer: {job.order.first_name} {job.order.surname}")
        self.stdout.write(f"Order ID: {job.order.id}")
        self.stdout.write(f"Location: {job.location.location_name}")
        self.stdout.write(f"Priority: {'High' if job.priority_flag else 'Normal'}")
        
        # Timestamps
        self.stdout.write(f"\n⏰ TIMELINE:")
        self.stdout.write(f"Created: {job.created_at}")
        if job.started_at:
            self.stdout.write(f"Started: {job.started_at}")
        if job.completed_at:
            self.stdout.write(f"Completed/Failed: {job.completed_at}")
        if job.reviewed_at:
            self.stdout.write(f"Reviewed: {job.reviewed_at}")
        
        # Error information
        self.stdout.write(f"\n❌ ERROR INFORMATION:")
        self.stdout.write(f"Retries: {job.retry_count}/{job.max_retries}")
        self.stdout.write(f"Failure Reason: {job.failure_reason or 'Unknown'}")
        
        if job.error_message:
            self.stdout.write(f"\nError Message:")
            self.stdout.write(f"{job.error_message}")
        
        if job.error_details:
            self.stdout.write(f"\nError Details:")
            self.stdout.write(json.dumps(job.error_details, indent=2))
        
        # Review information
        if job.reviewed_by or job.review_notes:
            self.stdout.write(f"\n👤 REVIEW INFORMATION:")
            if job.reviewed_by:
                self.stdout.write(f"Reviewed by: {job.reviewed_by}")
            if job.review_notes:
                self.stdout.write(f"Notes: {job.review_notes}")
        
        # Requeue information
        if job.requeue_reason:
            self.stdout.write(f"\n🔄 REQUEUE INFORMATION:")
            self.stdout.write(f"Reason: {job.requeue_reason}")
            self.stdout.write(f"Priority: {'High' if job.requeue_priority else 'Normal'}")

    def requeue_job(self, job_id, admin_user, priority=False, reason=None):
        """Requeue a job for processing"""
        try:
            job = QueuedJob.objects.get(id=job_id)
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
            return
        
        if job.status not in ['review', 'failed']:
            self.stdout.write(self.style.ERROR(f"Job {job_id} is not in review or failed status (current: {job.status})"))
            return
        
        # Requeue the job
        job.requeue_job(
            admin_user=admin_user,
            priority=priority,
            reason=reason or f"Requeued by {admin_user}"
        )
        
        priority_text = "high priority" if priority else "normal priority"
        self.stdout.write(self.style.SUCCESS(f"✓ Job {job_id} requeued with {priority_text}"))
        self.stdout.write(f"  Customer: {job.order.first_name} {job.order.surname}")
        self.stdout.write(f"  Location: {job.location.location_name}")
        if reason:
            self.stdout.write(f"  Reason: {reason}")

    def move_to_review(self, job_id, admin_user, notes=None):
        """Move a failed job to review queue"""
        try:
            job = QueuedJob.objects.get(id=job_id)
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
            return
        
        if job.status != 'failed':
            self.stdout.write(self.style.ERROR(f"Job {job_id} is not in failed status (current: {job.status})"))
            return
        
        # Move to review
        job.move_to_review(admin_user=admin_user, notes=notes)
        
        self.stdout.write(self.style.SUCCESS(f"✓ Job {job_id} moved to review queue"))
        self.stdout.write(f"  Customer: {job.order.first_name} {job.order.surname}")
        self.stdout.write(f"  Location: {job.location.location_name}")
        if notes:
            self.stdout.write(f"  Notes: {notes}")
