from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
from orders.models import order as Order  # Fix the import with alias

class Command(BaseCommand):
    help = 'Manually process a queued job'

    def add_arguments(self, parser):
        parser.add_argument(
            'job_id',
            type=str,
            help='ID of the job to process',
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force processing even if job is not in queued status',
        )

    def handle(self, *args, **options):
        job_id = options['job_id']
        force = options['force']
        
        try:
            # Get the job
            job = QueuedJob.objects.get(id=job_id)
            
            self.stdout.write(f"Found job {job.id} for order {job.order.id} with status: {job.status}")
            
            if job.status != 'queued' and not force:
                self.stdout.write(self.style.ERROR(f"Job is not in queued status. Use --force to process anyway."))
                return
            
            # Get the order ID
            order_id = job.order.id
            
            # Get the location ID
            location_id = job.location.id
            
            # Schedule the job for processing
            queue_name = f'location.{location_id}'
            self.stdout.write(f"Scheduling job for processing in queue: {queue_name}")
            
            # Use apply_async with explicit queue name
            task = process_order.apply_async(
                args=[str(order_id)],
                queue=queue_name
            )
            
            self.stdout.write(self.style.SUCCESS(f"Job {job.id} scheduled for processing with task ID: {task.id}"))
            
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error processing job: {str(e)}"))



