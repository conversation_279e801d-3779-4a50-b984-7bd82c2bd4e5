from django.urls import path
from . import views

app_name = 'queue_system'

urlpatterns = [
    # Dashboard Views
    path('admin/queue-overview/', views.queue_overview, name='queue_overview'),
    path('admin/location/<uuid:location_id>/', views.location_queue_details, name='location_queue_details'),
    path('admin/review-queue/', views.review_queue_dashboard, name='review_queue_dashboard'),
    path('admin/job/<int:job_id>/', views.job_details, name='job_details'),
    path('admin/job-details/<int:job_id>/', views.job_details, name='job_details_alt'),
    
    # AJAX Actions
    path('admin/adjust-workers/<uuid:location_id>/', views.adjust_workers, name='adjust_workers'),
    path('admin/job-action/<int:job_id>/', views.job_action, name='job_action'),
]
