{% extends "admin/index.html" %}
{% load i18n %}

{% block content %}
<div class="queue-dashboard-summary" style="margin-bottom: 20px; padding: 15px; background-color: #f8f8f8; border-radius: 4px;">
  <h2 style="margin-top: 0;">Queue System Dashboard</h2>
  
  <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-top: 15px;">
    <div style="flex: 1; min-width: 200px; background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
      <h3 style="margin-top: 0; color: #417690;">Active Jobs</h3>
      <div style="font-size: 24px; font-weight: bold; color: #417690;">{{ active_jobs }}</div>
      <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" style="display: block; margin-top: 10px;">View All Jobs</a>
    </div>
    
    <div style="flex: 1; min-width: 200px; background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
      <h3 style="margin-top: 0; color: #417690;">Failed Jobs</h3>
      <div style="font-size: 24px; font-weight: bold; color: #d32f2f;">{{ failed_jobs }}</div>
      <a href="{% url 'admin:queue_system_queuedjob_changelist' %}?status__exact=failed" style="display: block; margin-top: 10px;">View Failed Jobs</a>
    </div>
    
    <div style="flex: 1; min-width: 200px; background-color: white; padding: 15px; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
      <h3 style="margin-top: 0; color: #417690;">Active Workers</h3>
      <div style="font-size: 24px; font-weight: bold; color: #388e3c;">{{ active_workers }}</div>
      <a href="{% url 'admin:queue_system_locationqueueconfig_changelist' %}" style="display: block; margin-top: 10px;">View Queue Config</a>
    </div>
  </div>
  
  <div style="margin-top: 15px;">
    <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" class="button" style="background: #417690; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">Queue Dashboard</a>
    <a href="{% url 'admin:queue_system_joberror_changelist' %}" class="button" style="background: #bf4040; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin-left: 10px;">Error Dashboard</a>
  </div>
</div>

{{ block.super }}
{% endblock %}