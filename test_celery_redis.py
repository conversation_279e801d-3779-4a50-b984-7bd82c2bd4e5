#!/usr/bin/env python
"""
Test Celery Redis Connection
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from celery import Celery

def test_redis_connection():
    print("Testing Celery Redis connection...")
    
    # Create a simple Celery app
    app = Celery('test')
    app.conf.update(
        broker_url='redis://localhost:6379/0',
        result_backend='redis://localhost:6379/0',
        task_serializer='json',
        result_serializer='json',
        accept_content=['json'],
    )
    
    @app.task
    def test_task(message):
        return f"Received: {message}"
    
    try:
        # Test connection
        result = test_task.delay("Hello Redis!")
        print(f"Task sent successfully! Task ID: {result.id}")
        
        # Try to get result (this will timeout if worker not running, which is expected)
        try:
            value = result.get(timeout=5)
            print(f"Task result: {value}")
        except Exception as e:
            print(f"Could not get result (expected if no worker running): {e}")
            
        return True
        
    except Exception as e:
        print(f"Failed to send task: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    if success:
        print("✓ Celery can connect to Redis!")
    else:
        print("✗ Celery cannot connect to Redis")
        sys.exit(1)
