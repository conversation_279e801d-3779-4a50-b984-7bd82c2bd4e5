#!/usr/bin/env python
"""
Test sending tasks to Redis workers
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.tasks import process_order

def test_redis_task():
    print("Testing Redis task sending...")
    
    # Try to send a task to a specific queue
    try:
        result = process_order.apply_async(
            args=['test-order-id'],
            queue='location.744ae77f-8a7c-41d1-b9c8-00dd0fe020dd',
            retry=False
        )
        
        print(f"✓ Task sent successfully! Task ID: {result.id}")
        print(f"Task state: {result.state}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to send task: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_redis_task()
    if success:
        print("Redis task sending works!")
    else:
        print("Redis task sending failed!")
        sys.exit(1)
