from django.core.management.base import BaseCommand
from django.utils import timezone
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
from orders.models import order as Order
from locations.models import Location
import time

class Command(BaseCommand):
    help = 'Demonstrate the complete retry cycle'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🔄 RETRY MECHANISM DEMONSTRATION"))
        self.stdout.write("=" * 60)
        
        # Show current retry status
        self.show_retry_summary()
        
        self.stdout.write(f"\n🎯 HOW THE RETRY MECHANISM WORKS:")
        self.stdout.write("-" * 40)
        
        self.stdout.write(f"\n1️⃣ JOB FAILS:")
        self.stdout.write("   • Bot execution fails (e.g., no form data)")
        self.stdout.write("   • Error is logged to JobError table")
        self.stdout.write("   • retry_count is incremented")
        
        self.stdout.write(f"\n2️⃣ RETRY CHECK:")
        self.stdout.write("   • If retry_count < max_retries (3):")
        self.stdout.write("     → Job status reset to 'queued'")
        self.stdout.write("     → started_at and worker_id cleared")
        self.stdout.write("     → Order status set to 'bot_submission_retry'")
        self.stdout.write("   • If retry_count >= max_retries:")
        self.stdout.write("     → Job marked as 'failed'")
        self.stdout.write("     → Order status set to 'bot_submission_failed'")
        
        self.stdout.write(f"\n3️⃣ REQUEUE PROCESS:")
        self.stdout.write("   • Try Redis queue with 30-second delay")
        self.stdout.write("   • If Redis fails → Background thread processing")
        self.stdout.write("   • Job waits 30 seconds before retry attempt")
        
        self.stdout.write(f"\n4️⃣ RETRY ATTEMPT:")
        self.stdout.write("   • Job picked up from queue again")
        self.stdout.write("   • Status changed to 'processing'")
        self.stdout.write("   • Bot execution attempted again")
        self.stdout.write("   • Process repeats until success or max retries")
        
        self.stdout.write(f"\n📊 CURRENT SYSTEM STATUS:")
        self.stdout.write("-" * 30)
        
        # Show detailed status
        queued_retries = QueuedJob.objects.filter(status='queued', retry_count__gt=0)
        processing_jobs = QueuedJob.objects.filter(status='processing')
        failed_jobs = QueuedJob.objects.filter(status='failed', retry_count__gt=0)
        
        if queued_retries.exists():
            self.stdout.write(f"\n⏳ JOBS WAITING FOR RETRY ({queued_retries.count()}):")
            for job in queued_retries:
                time_since_created = timezone.now() - job.created_at
                minutes_waiting = time_since_created.total_seconds() / 60
                self.stdout.write(f"   Job {job.id}: {job.retry_count}/3 retries, waiting {minutes_waiting:.1f} min")
        
        if processing_jobs.exists():
            self.stdout.write(f"\n🔄 JOBS CURRENTLY PROCESSING ({processing_jobs.count()}):")
            for job in processing_jobs:
                if job.started_at:
                    time_running = timezone.now() - job.started_at
                    minutes_running = time_running.total_seconds() / 60
                    retry_info = f" (retry {job.retry_count})" if job.retry_count > 0 else ""
                    self.stdout.write(f"   Job {job.id}: Running {minutes_running:.1f} min{retry_info}")
        
        if failed_jobs.exists():
            self.stdout.write(f"\n❌ JOBS THAT FAILED AFTER RETRIES ({failed_jobs.count()}):")
            for job in failed_jobs:
                self.stdout.write(f"   Job {job.id}: Failed after {job.retry_count}/3 retries")
        
        self.stdout.write(f"\n✅ RETRY MECHANISM BENEFITS:")
        self.stdout.write("-" * 30)
        self.stdout.write("• ✅ Automatic recovery from temporary failures")
        self.stdout.write("• ✅ No jobs stuck in processing status")
        self.stdout.write("• ✅ Detailed error tracking and logging")
        self.stdout.write("• ✅ Configurable retry limits (currently 3)")
        self.stdout.write("• ✅ Fallback processing when Redis unavailable")
        self.stdout.write("• ✅ Order status tracking throughout retry cycle")
        
        self.stdout.write(f"\n🛠️ MANAGEMENT COMMANDS:")
        self.stdout.write("-" * 25)
        self.stdout.write("• Check retry status: python manage.py test_retry_mechanism")
        self.stdout.write("• Fix stuck jobs: python manage.py fix_stuck_jobs")
        self.stdout.write("• Create test job: python manage.py test_retry_mechanism --create-test")
        self.stdout.write("• Monitor job: python manage.py test_retry_mechanism --monitor-job <id>")

    def show_retry_summary(self):
        total_jobs = QueuedJob.objects.count()
        jobs_with_retries = QueuedJob.objects.filter(retry_count__gt=0).count()
        queued_retries = QueuedJob.objects.filter(status='queued', retry_count__gt=0).count()
        failed_after_retries = QueuedJob.objects.filter(status='failed', retry_count__gt=0).count()
        
        self.stdout.write(f"📈 RETRY STATISTICS:")
        self.stdout.write(f"   Total Jobs: {total_jobs}")
        self.stdout.write(f"   Jobs with Retries: {jobs_with_retries}")
        self.stdout.write(f"   Waiting for Retry: {queued_retries}")
        self.stdout.write(f"   Failed after Retries: {failed_after_retries}")
        
        if jobs_with_retries > 0:
            retry_rate = (jobs_with_retries / total_jobs) * 100
            self.stdout.write(f"   Retry Rate: {retry_rate:.1f}%")
        
        # Show retry distribution
        retry_counts = {}
        for job in QueuedJob.objects.filter(retry_count__gt=0):
            count = retry_counts.get(job.retry_count, 0)
            retry_counts[job.retry_count] = count + 1
        
        if retry_counts:
            self.stdout.write(f"\n📊 RETRY DISTRIBUTION:")
            for retry_count, job_count in sorted(retry_counts.items()):
                self.stdout.write(f"   {retry_count} retries: {job_count} jobs")
