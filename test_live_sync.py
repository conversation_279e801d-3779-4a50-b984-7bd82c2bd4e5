#!/usr/bin/env python
"""
Test the live database sync functionality (every 5 seconds)
"""

import requests
import time
import subprocess
import os
import sys

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, '.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    import django
    django.setup()

def get_worker_counts():
    """Get current worker counts from different sources"""
    
    # 1. Get from tasklist
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True)
        tasklist_count = len([line for line in result.stdout.split('\n') if 'celery.exe' in line])
    except:
        tasklist_count = 0
    
    # 2. Get from Celery inspect
    try:
        setup_django()
        from config.celery import app
        inspect = app.control.inspect()
        workers = inspect.active()
        celery_count = len([w for w in workers.keys() if workers[w] is not None]) if workers else 0
    except:
        celery_count = 0
    
    # 3. Get from database API
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/')
        if response.status_code == 200:
            data = response.json()
            db_count = data['overview']['total_workers']
        else:
            db_count = 0
    except:
        db_count = 0
    
    return tasklist_count, celery_count, db_count

def test_manual_sync():
    """Test manual sync with silent mode"""
    print("🔧 Testing manual sync (silent mode)...")
    try:
        result = subprocess.run(['python', 'manage.py', 'sync_worker_status', '--silent'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Manual silent sync completed")
            return True
        else:
            print(f"   ❌ Manual sync failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Error running manual sync: {e}")
        return False

def monitor_live_sync(duration=30):
    """Monitor live sync for specified duration"""
    print(f"📡 MONITORING LIVE SYNC FOR {duration} SECONDS")
    print("=" * 60)
    print("Time    | Tasklist | Celery | Database | Status")
    print("-" * 60)
    
    start_time = time.time()
    last_db_count = None
    sync_detected = False
    
    while time.time() - start_time < duration:
        tasklist_count, celery_count, db_count = get_worker_counts()
        
        # Check if database changed (indicating sync occurred)
        status = "📊"
        if last_db_count is not None and last_db_count != db_count:
            status = "🔄 SYNC!"
            sync_detected = True
        
        elapsed = int(time.time() - start_time)
        print(f"{elapsed:2d}s     | {tasklist_count:8d} | {celery_count:6d} | {db_count:8d} | {status}")
        
        last_db_count = db_count
        time.sleep(2)  # Check every 2 seconds
    
    print("-" * 60)
    return sync_detected

def test_api_responsiveness():
    """Test API response times"""
    print("\n⚡ TESTING API RESPONSIVENESS")
    print("=" * 50)
    
    urls = [
        ('Overview API', 'http://localhost:8000/queue/api/live-stats/'),
        ('Location API', 'http://localhost:8000/queue/api/live-stats/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/')
    ]
    
    for name, url in urls:
        try:
            start_time = time.time()
            response = requests.get(url)
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                print(f"   ✅ {name}: {response_time:.1f}ms")
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")

def main():
    """Main test function"""
    print("🚀 TESTING LIVE DATABASE SYNC (5 SECOND INTERVALS)")
    print("=" * 70)
    
    # Initial status
    print("\n📊 INITIAL STATUS")
    print("=" * 50)
    tasklist_count, celery_count, db_count = get_worker_counts()
    print(f"Tasklist processes: {tasklist_count}")
    print(f"Celery workers: {celery_count}")
    print(f"Database workers: {db_count}")
    
    # Test manual sync
    print(f"\n🔧 TESTING MANUAL SYNC")
    print("=" * 50)
    manual_sync_works = test_manual_sync()
    
    if manual_sync_works:
        # Check if counts changed
        new_tasklist, new_celery, new_db = get_worker_counts()
        print(f"After sync - Database: {db_count} → {new_db}")
        
        if new_db != db_count:
            print("   ✅ Manual sync updated database")
        else:
            print("   ✅ Manual sync completed (no changes needed)")
    
    # Test API responsiveness
    test_api_responsiveness()
    
    # Monitor live sync
    print(f"\n📡 LIVE SYNC MONITORING")
    print("=" * 50)
    print("Watching for automatic sync every 5 seconds...")
    print("(Database should update automatically if workers change)")
    
    sync_detected = monitor_live_sync(30)
    
    # Summary
    print(f"\n📋 TEST SUMMARY")
    print("=" * 50)
    
    if manual_sync_works:
        print("✅ Manual sync: WORKING")
    else:
        print("❌ Manual sync: FAILED")
    
    if sync_detected:
        print("✅ Live sync: DETECTED (database updated automatically)")
    else:
        print("⚠️ Live sync: NO CHANGES DETECTED")
        print("   (This is normal if worker count is stable)")
    
    print(f"\n🎯 LIVE SYNC CONFIGURATION:")
    print("   • Database sync: Every 5 seconds")
    print("   • UI updates: Every 5 seconds")
    print("   • Silent mode: Enabled (reduced logging)")
    
    print(f"\n🔧 MANUAL TESTING:")
    print("   1. Open: http://localhost:8000/queue/admin/queue-overview/")
    print("   2. Watch worker counts update every 5 seconds")
    print("   3. Start/stop workers and see immediate updates")
    print("   4. Use: python manage.py sync_worker_status --silent")

if __name__ == '__main__':
    main()
