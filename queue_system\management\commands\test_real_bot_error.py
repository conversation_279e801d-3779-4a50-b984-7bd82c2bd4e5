from django.core.management.base import BaseCommand
from django.utils import timezone
from orders.models import order as Order
from locations.models import Location
from orders.models import BarbadosForm
from queue_system.models import QueuedJob, JobError
import uuid

class Command(BaseCommand):
    help = 'Create a test job with form data to trigger actual bot execution error'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🧪 CREATING TEST JOB WITH FORM DATA FOR BOT ERROR"))
        self.stdout.write("=" * 70)
        
        # Get Barbados location
        barbados_location = Location.objects.filter(location_name__icontains='barbados').first()
        if not barbados_location:
            self.stdout.write(self.style.ERROR("No Barbados location found"))
            return
        
        # Create test order
        order = Order.objects.create(
            first_name="BotError",
            surname="Test",
            customer_email="<EMAIL>",
            location=barbados_location,
            status='criminal_check_passed'
        )
        
        # Create BarbadosForm with INVALID data to trigger bot errors
        barbados_form = BarbadosForm.objects.create(
            order=order,
            passport_number="INVALID123",
            nationality="Invalid Country",  # This will cause dropdown failure
            place_of_birth="Invalid City",
            father_name="Test Father",
            mother_name="Test Mother",
            occupation="Invalid Occupation",  # This will cause dropdown failure
            employer="Test Employer",
            purpose_of_visit="Invalid Purpose",  # This will cause dropdown failure
            duration_of_stay=999,  # Invalid duration
            accommodation_address="Invalid Address",
            travel_date=timezone.now().date() + timezone.timedelta(days=30),
            # Add fields that will cause bot to fail
            residential_status="Invalid Status",  # This will cause the bot to fail
            how_are_you_entering="Invalid Method",  # This will cause the bot to fail
            airline="Invalid Airline"  # This will cause dropdown failure
        )
        
        self.stdout.write(f"✅ Created order {order.id} with INVALID BarbadosForm data")
        self.stdout.write(f"   Customer: {order.first_name} {order.surname}")
        self.stdout.write(f"   Location: {barbados_location.location_name}")
        self.stdout.write(f"   Form data: Complete but INVALID (will trigger bot errors)")
        
        # Create job manually
        job = QueuedJob.objects.create(
            order=order,
            location=barbados_location,
            status='queued',
            max_retries=3,
            retry_count=0
        )
        
        self.stdout.write(f"✅ Created job {job.id}")
        
        # Process the job directly to see the actual bot error
        self.stdout.write(f"\n🚀 PROCESSING JOB TO TRIGGER REAL BOT ERROR...")
        self.stdout.write("-" * 50)
        self.stdout.write("This will trigger actual Selenium/bot execution errors:")
        self.stdout.write("- Invalid residential status")
        self.stdout.write("- Invalid entry method") 
        self.stdout.write("- Invalid dropdown selections")
        self.stdout.write("- Element not found errors")
        
        # Import and run the task directly
        from queue_system.tasks import process_order
        
        try:
            result = process_order(str(order.id))
            self.stdout.write(f"Job result: {result}")
        except Exception as e:
            self.stdout.write(f"Job failed with exception: {str(e)}")
        
        # Check job status and errors
        job.refresh_from_db()
        self.stdout.write(f"\n📊 JOB STATUS AFTER PROCESSING:")
        self.stdout.write(f"   Status: {job.status}")
        self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
        self.stdout.write(f"   Failure Reason: {job.failure_reason}")
        self.stdout.write(f"   Error Message: {job.error_message}")
        
        # Check JobError records
        errors = JobError.objects.filter(job=job)
        self.stdout.write(f"\n🔍 JOB ERRORS ({errors.count()}):")
        for i, error in enumerate(errors, 1):
            self.stdout.write(f"   Error {i}: {error.error_message}")
            if error.error_trace:
                self.stdout.write(f"   Trace preview: {error.error_trace[:200]}...")
        
        self.stdout.write(f"\n🌐 VIEW JOB DETAILS:")
        self.stdout.write(f"   Job Details: http://localhost:8000/queue/admin/job-details/{job.id}/")
        self.stdout.write(f"   Review Queue: http://localhost:8000/queue/admin/review-queue/")
        
        self.stdout.write(f"\n💡 EXPECTED ERRORS:")
        self.stdout.write(f"   - 'Problem with Residential Status in database: Invalid Status'")
        self.stdout.write(f"   - 'Problem with Entry Method in database: Invalid Method'")
        self.stdout.write(f"   - Selenium TimeoutException or NoSuchElementException")
        self.stdout.write(f"   - These should be categorized as 'invalid_order_data' or 'bot_execution_error'")
        
        return f"Created job {job.id} for testing real bot errors"
