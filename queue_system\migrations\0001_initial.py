# Generated by Django 4.2.21 on 2025-06-11 17:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('locations', '0007_rename_application_price_location_traveller_price'),
        ('orders', '0012_alter_barbadosform_country_of_birth_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='QueuedJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='queued', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('scheduled_for', models.DateTimeField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('priority_flag', models.BooleanField(default=False)),
                ('worker_id', models.CharField(blank=True, max_length=100, null=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='locations.location')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='queue_jobs', to='orders.order')),
            ],
            options={
                'verbose_name': 'Queued Job',
                'verbose_name_plural': 'Queued Jobs',
                'ordering': ['-priority_flag', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='LocationQueueConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('has_time_window', models.BooleanField(default=False, help_text='If enabled, jobs will only be processed within a specific time window before travel date')),
                ('window_days_before_travel', models.PositiveIntegerField(blank=True, help_text='Number of days before travel date when processing should begin', null=True)),
                ('max_workers', models.PositiveIntegerField(default=1, help_text='Maximum number of concurrent workers for this location')),
                ('active_workers', models.PositiveIntegerField(default=0, help_text='Current number of active workers')),
                ('priority_level', models.PositiveIntegerField(default=1, help_text='Priority level for this location (higher = more priority)')),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='queue_config', to='locations.location')),
            ],
            options={
                'verbose_name': 'Location Queue Configuration',
                'verbose_name_plural': 'Location Queue Configurations',
            },
        ),
        migrations.CreateModel(
            name='JobError',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('occurred_at', models.DateTimeField(auto_now_add=True)),
                ('error_message', models.TextField()),
                ('error_trace', models.TextField(blank=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='errors', to='queue_system.queuedjob')),
            ],
            options={
                'verbose_name': 'Job Error',
                'verbose_name_plural': 'Job Errors',
                'ordering': ['-occurred_at'],
            },
        ),
    ]
