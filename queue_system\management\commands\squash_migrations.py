from django.core.management.base import BaseCommand
import subprocess

class Command(BaseCommand):
    help = 'Squash migrations for queue_system app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='initial',
            help='Name for the squashed migration',
        )

    def handle(self, *args, **options):
        name = options['name']
        
        self.stdout.write("Squashing migrations for queue_system app...")
        
        # Get the latest migration
        result = subprocess.run(
            ['python', 'manage.py', 'showmigrations', 'queue_system'],
            capture_output=True,
            text=True
        )
        
        # Parse the output to find the latest migration
        migrations = []
        for line in result.stdout.splitlines():
            if '[X]' in line:
                migration_name = line.split('[X]')[1].strip()
                migrations.append(migration_name)
        
        if not migrations:
            self.stdout.write(self.style.ERROR("No applied migrations found"))
            return
        
        latest_migration = migrations[-1]
        
        # Squash migrations
        self.stdout.write(f"Squashing migrations up to {latest_migration}...")
        
        squash_cmd = [
            'python', 'manage.py', 'squashmigrations', 
            'queue_system', latest_migration,
            '--squashed-name', name
        ]
        
        result = subprocess.run(squash_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            self.stdout.write(self.style.SUCCESS("Migrations squashed successfully"))
            self.stdout.write(result.stdout)
        else:
            self.stdout.write(self.style.ERROR("Error squashing migrations"))
            self.stdout.write(result.stderr)
        
        # Show migration status after squashing
        self.stdout.write("Migration status after squashing:")
        subprocess.run(['python', 'manage.py', 'showmigrations', 'queue_system'])