from django.core.management.base import BaseCommand
from queue_system.tasks import process_queued_jobs

class Command(BaseCommand):
    help = 'Manually run the process_queued_jobs task'

    def handle(self, *args, **options):
        self.stdout.write("Running process_queued_jobs task...")
        
        try:
            result = process_queued_jobs()
            self.stdout.write(self.style.SUCCESS(f"Task completed: {result}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Task failed: {str(e)}"))
            import traceback
            self.stdout.write(traceback.format_exc())
