#!/bin/bash

# Check if the migrations directory exists
if [ ! -d "queue_system/migrations" ]; then
    echo "Error: migrations directory not found"
    exit 1
fi

# Remove the non-numbered migration file
echo "Removing add_auto_scale_fields.py..."
rm -f queue_system/migrations/add_auto_scale_fields.py

# Check if the migration record exists in the database
echo "Checking migration records..."
python -c "
from django.db import connection
with connection.cursor() as cursor:
    cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'\")
    migration = cursor.fetchone()
    if migration:
        print('Migration record exists, updating it...')
        cursor.execute(\"UPDATE django_migrations SET name='0003_add_auto_scale_fields' WHERE app='queue_system' AND name='add_auto_scale_fields'\")
        print('Migration record updated')
    else:
        print('Migration record not found')
"

# Show migration status
echo "Migration status after fix:"
python manage.py showmigrations queue_system

# Try to apply migrations
echo "Applying migrations..."
python manage.py migrate queue_system