#!/usr/bin/env python
"""
Test with explicit Redis configuration
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from celery import Celery

def test_explicit_redis():
    print("Testing with explicit Redis configuration...")
    
    # Create a new Celery app with explicit Redis config
    app = Celery('test_redis')
    
    # Explicitly set Redis configuration
    app.conf.update(
        broker_url='redis://localhost:6379/0',
        result_backend='redis://localhost:6379/0',
        task_serializer='json',
        result_serializer='json',
        accept_content=['json'],
        task_routes={},
        task_default_queue='default',
        task_create_missing_queues=True,
    )
    
    print(f"Broker URL: {app.conf.broker_url}")
    print(f"Result Backend: {app.conf.result_backend}")
    
    @app.task
    def test_task(message):
        return f"Processed: {message}"
    
    try:
        # Send a task
        result = test_task.apply_async(
            args=['Hello Redis!'],
            queue='default'
        )
        
        print(f"✓ Task sent successfully! Task ID: {result.id}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to send task: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_explicit_redis()
    if success:
        print("✓ Explicit Redis configuration works!")
    else:
        print("✗ Explicit Redis configuration failed!")
        sys.exit(1)
