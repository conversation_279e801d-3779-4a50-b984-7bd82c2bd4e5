import os
import subprocess
from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig

class Command(BaseCommand):
    help = 'Start Celery workers for all locations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--location',
            type=str,
            help='Start workers only for a specific location (by name)',
        )

        parser.add_argument(
            '--init-queues',
            action='store_true',
            dest='init_queues',
            help='Initialize all queues in broker'
        )
        
        parser.add_argument(
            '--scheduler',
            action='store_true',
            help='Start the scheduler worker',
        )
        
        parser.add_argument(
            '--beat',
            action='store_true',
            help='Start the Celery beat scheduler for periodic tasks',
        )
        
        parser.add_argument(
            '--concurrency',
            type=int,
            default=None,
            help='Override the concurrency setting for workers',
        )
        
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Run in debug mode with more verbose output',
        )
        
        parser.add_argument(
            '--respect-config',
            action='store_true',
            help='Respect the configuration in the database',
        )

        parser.add_argument(
            '--no-auto-process',
            action='store_true',
            help='Disable automatic job processing worker',
        )

    def _init_queues(self):
        """Initialize all required queues"""
        from locations.models import Location
                
        location_ids = Location.objects.values_list('id', flat=True)
        queue_list = [f'location.{loc_id}' for loc_id in location_ids]
        queue_arg = ','.join(queue_list)



        cmd = [
            'celery',
            '-A', 'config',  # Replace 'config' with your actual celery app name
            'worker',
            '-Q', queue_arg,  # Multiple queues joined by commas
            '-c', '4',         # You can adjust concurrency as needed
            '--loglevel=info',
            '-n', 'multi-location-worker@%h'
        ]

        self.stdout.write(self.style.SUCCESS(
            f"Starting worker for queues: {queue_arg}"
        ))

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        self.stdout.write(f"Worker process started with PID: {process.pid}")


    def handle(self, *args, **options):
        # Get all location configs
        if options['location']:
            # Filter by location name
            configs = LocationQueueConfig.objects.filter(
                location__location_name__icontains=options['location']
            )
            if not configs.exists():
                self.stderr.write(self.style.ERROR(f"No location found matching '{options['location']}'"))
                return
        else:
            # Get all configs
            configs = LocationQueueConfig.objects.all()
            
        # Start location-specific workers
        for config in configs:
            location_id = config.location.id
            location_name = config.location.location_name
            
            # Determine concurrency (number of worker processes)
            if options['respect_config']:
                # Use the configuration from the database
                concurrency = config.max_workers
            else:
                # Use the command line argument or the database config
                concurrency = options['concurrency'] if options['concurrency'] is not None else config.max_workers
            
            # Build the Celery worker command
            cmd = [
                'celery', 
                '-A', 'config', 
                'worker',
                '-Q', f'location.{location_id}',
                '-c', str(concurrency),
                '--loglevel=info' if not options['debug'] else '--loglevel=debug',
                '-n', f'worker.{location_name.replace(" ", "_")}@%h'
            ]
            
            self.stdout.write(self.style.SUCCESS(
                f"Starting {concurrency} worker(s) for location: {location_name} (Queue: location.{location_id})"
            ))
            
            # Start the worker process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # Update active workers count in the database
            config.active_workers = concurrency
            config.save()
            
            self.stdout.write(f"Worker process started with PID: {process.pid}")
        
        # Start scheduler worker if requested
        if options['scheduler']:
            self.stdout.write(self.style.SUCCESS("Starting scheduler worker"))
            
            # Build the scheduler worker command
            cmd = [
                'celery',
                '-A', 'config',
                'worker',
                '-Q', 'scheduler,error',
                '-c', '1',
                '--loglevel=info' if not options['debug'] else '--loglevel=debug',
                '-n', 'worker.scheduler@%h'
            ]
            
            # Start the scheduler worker process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.stdout.write(f"Scheduler worker process started with PID: {process.pid}")

        # Initialize queues if requested
        if options['init_queues']:
            if not self._init_queues():
                return  # Exit if queue init fails
            
            self._init_queues()
            
        
        # Start Celery beat if requested
        if options['beat']:
            self.stdout.write(self.style.SUCCESS("Starting Celery beat scheduler"))
            
            # Build the beat command
            cmd = [
                'celery',
                '-A', 'config',
                'beat',
                '--loglevel=debug'
                # '--loglevel=info' if not options['debug'] else '--loglevel=debug'
            ]
            
            # Start the beat process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.stdout.write(f"Celery beat process started with PID: {process.pid}")
        
        # Start auto-processing worker unless disabled
        if not options.get('no_auto_process', False):
            self.stdout.write(self.style.SUCCESS("Starting auto-processing worker"))

            # Build the auto-processing worker command
            cmd = [
                'python', 'manage.py', 'auto_worker',
                '--interval=20',
                '--max-jobs-per-cycle=3'
            ]

            # Start the auto-processing worker process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            self.stdout.write(f"Auto-processing worker started with PID: {process.pid}")

        self.stdout.write(self.style.SUCCESS(
            "All requested workers have been started. Use 'ps aux | grep celery' to view running processes."
        ))
        
        # Note: In a production environment, you would typically use a process manager
        # like supervisord to manage these processes instead of this command.
        self.stdout.write(self.style.WARNING(
            "Note: This command starts workers in the background. For production use, "
            "consider using supervisord or systemd to manage Celery workers."
        ))



