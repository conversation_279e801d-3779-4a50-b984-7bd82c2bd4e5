import subprocess
from django.core.management.base import BaseCommand
from queue_system.models import LocationQueueConfig

class Command(BaseCommand):
    help = 'Check the status of Celery workers and queues'

    def handle(self, *args, **options):
        self.stdout.write("Checking Celery worker status...")
        
        # Run celery inspect active
        try:
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'active'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nActive tasks:")
            self.stdout.write(result.stdout)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking active tasks: {str(e)}"))
        
        # Run celery inspect active_queues
        try:
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'active_queues'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nActive queues:")
            self.stdout.write(result.stdout)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking active queues: {str(e)}"))
        
        # Run celery inspect stats
        try:
            result = subprocess.run(
                ['celery', '-A', 'config', 'inspect', 'stats'],
                capture_output=True,
                text=True
            )
            
            self.stdout.write("\nWorker stats:")
            self.stdout.write(result.stdout)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error checking worker stats: {str(e)}"))
        
        # Check database configuration
        self.stdout.write("\nDatabase configuration:")
        configs = LocationQueueConfig.objects.all()
        
        for config in configs:
            location_name = config.location.location_name
            location_id = config.location.id
            active = config.active_workers
            max_workers = config.max_workers
            
            # Count queued jobs for this location
            from queue_system.models import QueuedJob
            queued_count = QueuedJob.objects.filter(
                location=config.location,
                status='queued'
            ).count()
            
            if active > 0:
                self.stdout.write(self.style.SUCCESS(
                    f"{location_name} (ID: {location_id}): {active}/{max_workers} workers active, {queued_count} jobs queued"
                ))
            else:
                self.stdout.write(self.style.WARNING(
                    f"{location_name} (ID: {location_id}): No active workers (max: {max_workers}), {queued_count} jobs queued"
                ))