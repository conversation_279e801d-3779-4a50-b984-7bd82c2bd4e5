from django.core.management.base import BaseCommand
from django.utils import timezone
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
from orders.models import order as Order
from locations.models import Location
import time

class Command(BaseCommand):
    help = 'Test the job retry mechanism'

    def add_arguments(self, parser):
        parser.add_argument('--create-test', action='store_true', help='Create a test job that will fail')
        parser.add_argument('--monitor-job', type=int, help='Monitor a specific job ID')

    def handle(self, *args, **options):
        if options['create_test']:
            self.create_test_job()
        elif options['monitor_job']:
            self.monitor_job(options['monitor_job'])
        else:
            self.show_retry_status()

    def create_test_job(self):
        self.stdout.write(self.style.SUCCESS("🧪 CREATING TEST JOB FOR RETRY MECHANISM"))
        self.stdout.write("=" * 60)
        
        # Create a test order that will fail (no Barbados form data)
        try:
            # Get a location
            location = Location.objects.first()
            if not location:
                self.stdout.write(self.style.ERROR("No locations found. Please create a location first."))
                return
            
            # Create a test order
            order = Order.objects.create(
                first_name="Test",
                surname="Retry",
                customer_email="<EMAIL>",
                location=location,
                status="criminal_check_passed"
            )
            
            # Create a job manually
            job = QueuedJob.objects.create(
                order=order,
                location=location,
                status='queued',
                max_retries=3,
                retry_count=0
            )
            
            self.stdout.write(f"✅ Created test job {job.id} for order {order.id}")
            self.stdout.write(f"   Customer: {order.first_name} {order.surname}")
            self.stdout.write(f"   Location: {location.location_name}")
            self.stdout.write(f"   This job will fail because there's no Barbados form data")
            
            # Process the job to trigger failure and retry
            self.stdout.write(f"\n🚀 Starting job processing...")
            try:
                result = process_order(str(order.id))
                self.stdout.write(f"   Process result: {result}")
            except Exception as e:
                self.stdout.write(f"   Process error: {str(e)}")
            
            # Check job status after processing
            job.refresh_from_db()
            self.stdout.write(f"\n📊 Job status after processing:")
            self.stdout.write(f"   Status: {job.status}")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"   Error: {job.error_message}")
            
            self.stdout.write(f"\n💡 Monitor this job with: python manage.py test_retry_mechanism --monitor-job {job.id}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating test job: {str(e)}"))

    def monitor_job(self, job_id):
        self.stdout.write(self.style.SUCCESS(f"👀 MONITORING JOB {job_id}"))
        self.stdout.write("=" * 50)
        
        try:
            job = QueuedJob.objects.get(id=job_id)
            
            self.stdout.write(f"📋 Job Details:")
            self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
            self.stdout.write(f"   Location: {job.location.location_name}")
            self.stdout.write(f"   Created: {job.created_at}")
            
            # Monitor for 2 minutes
            for i in range(24):  # 24 * 5 seconds = 2 minutes
                job.refresh_from_db()
                
                self.stdout.write(f"\n⏰ Check {i+1}/24:")
                self.stdout.write(f"   Status: {job.status}")
                self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
                
                if job.started_at:
                    self.stdout.write(f"   Started: {job.started_at}")
                
                if job.error_message:
                    self.stdout.write(f"   Error: {job.error_message[:100]}...")
                
                if job.status in ['completed', 'failed', 'review']:
                    self.stdout.write(f"\n🏁 Job finished with status: {job.status}")
                    break
                
                time.sleep(5)  # Wait 5 seconds between checks
            
            # Final status
            job.refresh_from_db()
            self.stdout.write(f"\n📊 FINAL STATUS:")
            self.stdout.write(f"   Status: {job.status}")
            self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
            self.stdout.write(f"   Completed: {job.completed_at}")
            
            if job.error_message:
                self.stdout.write(f"   Error: {job.error_message}")
            
        except QueuedJob.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Job {job_id} not found"))

    def show_retry_status(self):
        self.stdout.write(self.style.SUCCESS("🔄 RETRY MECHANISM STATUS"))
        self.stdout.write("=" * 50)
        
        # Show jobs with retries
        retry_jobs = QueuedJob.objects.filter(retry_count__gt=0).order_by('-created_at')
        
        if retry_jobs.exists():
            self.stdout.write(f"📊 Jobs with retries ({retry_jobs.count()}):")
            for job in retry_jobs[:10]:  # Show last 10
                self.stdout.write(f"\n🔄 Job {job.id}:")
                self.stdout.write(f"   Customer: {job.order.first_name} {job.order.surname}")
                self.stdout.write(f"   Status: {job.status}")
                self.stdout.write(f"   Retries: {job.retry_count}/{job.max_retries}")
                self.stdout.write(f"   Created: {job.created_at}")
                
                if job.error_message:
                    self.stdout.write(f"   Error: {job.error_message[:80]}...")
        else:
            self.stdout.write("✅ No jobs with retries found")
        
        # Show current processing jobs
        processing_jobs = QueuedJob.objects.filter(status='processing')
        if processing_jobs.exists():
            self.stdout.write(f"\n🔄 Currently processing ({processing_jobs.count()}):")
            for job in processing_jobs:
                if job.started_at:
                    time_running = timezone.now() - job.started_at
                    minutes = time_running.total_seconds() / 60
                    self.stdout.write(f"   Job {job.id}: Running for {minutes:.1f} minutes")
        
        # Show failed jobs
        failed_jobs = QueuedJob.objects.filter(status='failed').order_by('-completed_at')
        if failed_jobs.exists():
            self.stdout.write(f"\n❌ Recently failed ({failed_jobs.count()}):")
            for job in failed_jobs[:5]:  # Show last 5
                self.stdout.write(f"   Job {job.id}: {job.retry_count}/{job.max_retries} retries")
        
        self.stdout.write(f"\n🧪 TESTING:")
        self.stdout.write(f"   Create test job: python manage.py test_retry_mechanism --create-test")
        self.stdout.write(f"   Monitor job: python manage.py test_retry_mechanism --monitor-job <job_id>")
        self.stdout.write(f"   Fix stuck jobs: python manage.py fix_stuck_jobs")
