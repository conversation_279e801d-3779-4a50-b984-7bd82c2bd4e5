#!/usr/bin/env python
"""
Verify that the worker sync issue is fixed
"""

import requests
import subprocess
import os
import sys

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, '.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    import django
    django.setup()

def check_actual_workers():
    """Check actual running Celery workers"""
    print("🔍 CHECKING ACTUAL CELERY WORKERS")
    print("=" * 50)
    
    # Check via tasklist
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True)
        if 'celery.exe' in result.stdout:
            lines = [line for line in result.stdout.split('\n') if 'celery.exe' in line]
            print(f"   ✅ Tasklist shows {len(lines)} Celery processes")
        else:
            print("   ❌ No Celery processes found via tasklist")
    except Exception as e:
        print(f"   ❌ Error checking tasklist: {e}")
    
    # Check via Celery inspect
    try:
        setup_django()
        from config.celery import app
        inspect = app.control.inspect()
        workers = inspect.active()
        if workers:
            responsive_workers = [w for w in workers.keys() if workers[w] is not None]
            print(f"   ✅ Celery inspect shows {len(responsive_workers)} responsive workers")
            return len(responsive_workers)
        else:
            print("   ❌ No workers responding to Celery inspect")
            return 0
    except Exception as e:
        print(f"   ❌ Error with Celery inspect: {e}")
        return 0

def check_database_status():
    """Check database worker status"""
    print("\n📊 CHECKING DATABASE STATUS")
    print("=" * 50)
    
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/')
        if response.status_code == 200:
            data = response.json()
            total_workers = data['overview']['total_workers']
            total_max = data['overview']['total_max_workers']
            print(f"   ✅ Database shows {total_workers}/{total_max} workers")
            
            # Show per-location breakdown
            active_locations = [loc for loc in data['overview']['locations'] if loc['active_workers'] > 0]
            print(f"   ✅ {len(active_locations)} locations have active workers:")
            for loc in active_locations:
                print(f"      • {loc['name']}: {loc['active_workers']}/{loc['max_workers']}")
            
            return total_workers
        else:
            print(f"   ❌ API error: {response.status_code}")
            return 0
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
        return 0

def check_admin_ui():
    """Check admin UI accessibility"""
    print("\n🖥️ CHECKING ADMIN UI")
    print("=" * 50)
    
    try:
        # Test overview page
        response = requests.get('http://localhost:8000/queue/admin/queue-overview/')
        if response.status_code == 200:
            print("   ✅ Queue overview page accessible")
        else:
            print(f"   ❌ Overview page error: {response.status_code}")
        
        # Test location details page
        response = requests.get('http://localhost:8000/queue/admin/location/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/')
        if response.status_code == 200:
            print("   ✅ Location details page accessible")
        else:
            print(f"   ❌ Location page error: {response.status_code}")
        
        # Test live stats API
        response = requests.get('http://localhost:8000/queue/api/live-stats/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/')
        if response.status_code == 200:
            data = response.json()
            workers = data['location']['active_workers']
            print(f"   ✅ Live stats API working (shows {workers} workers for test location)")
        else:
            print(f"   ❌ Live stats API error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error checking admin UI: {e}")

def main():
    """Main verification function"""
    print("🚀 VERIFYING WORKER SYNC FIX")
    print("=" * 70)
    
    # Check actual workers
    celery_workers = check_actual_workers()
    
    # Check database status
    db_workers = check_database_status()
    
    # Check admin UI
    check_admin_ui()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 70)
    
    if celery_workers > 0 and db_workers > 0:
        if abs(celery_workers - db_workers) <= 2:  # Allow small discrepancy
            print("✅ SYNC STATUS: WORKING")
            print(f"   • Celery workers: {celery_workers}")
            print(f"   • Database workers: {db_workers}")
            print("   • Difference is within acceptable range")
        else:
            print("⚠️ SYNC STATUS: PARTIAL")
            print(f"   • Celery workers: {celery_workers}")
            print(f"   • Database workers: {db_workers}")
            print("   • Significant difference detected")
            print("   • Run: python manage.py sync_worker_status")
    elif celery_workers == 0 and db_workers == 0:
        print("✅ SYNC STATUS: CLEAN")
        print("   • No workers running")
        print("   • Database correctly shows 0")
    else:
        print("❌ SYNC STATUS: OUT OF SYNC")
        print(f"   • Celery workers: {celery_workers}")
        print(f"   • Database workers: {db_workers}")
        print("   • Manual sync required")
    
    print("\n🔧 MANUAL TESTING:")
    print("   1. Open: http://localhost:8000/queue/admin/queue-overview/")
    print("   2. Verify worker counts match reality")
    print("   3. Watch for live updates (every 15 seconds)")
    print("   4. Test worker controls in location details")
    
    print("\n🔄 AUTOMATIC SYNC:")
    print("   • Added periodic sync task (every 5 minutes)")
    print("   • Prevents future sync issues")
    print("   • Manual sync: python manage.py sync_worker_status")

if __name__ == '__main__':
    main()
