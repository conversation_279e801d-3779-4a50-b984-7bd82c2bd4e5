from django.db import models
from django.utils import timezone

# Import the correct model names with their exact case
from orders.models import order as Order  # Alias to match our code
from locations.models import Location

class LocationQueueConfig(models.Model):
    """Configuration for location-specific queue settings"""
    location = models.OneToOneField(Location, on_delete=models.CASCADE, related_name='queue_config')
    has_time_window = models.BooleanField(default=False, 
                                         help_text="If enabled, jobs will only be processed within a specific time window before travel date")
    window_days_before_travel = models.PositiveIntegerField(null=True, blank=True,
                                                          help_text="Number of days before travel date when processing should begin")
    max_workers = models.PositiveIntegerField(default=1,
                                             help_text="Maximum number of concurrent workers for this location")
    active_workers = models.PositiveIntegerField(default=0,
                                               help_text="Current number of active workers")
    priority_level = models.PositiveIntegerField(default=1,
                                               help_text="Priority level for this location (higher numbers = higher priority)")
    auto_scale = models.BooleanField(default=True,
                                    help_text="Automatically scale workers based on queue length")
    min_workers = models.PositiveIntegerField(default=0,
                                             help_text="Minimum number of workers to keep running")
    
    def __str__(self):
        return f"{self.location.location_name} Queue Config"
    
    class Meta:
        verbose_name = "Location Queue Configuration"
        verbose_name_plural = "Location Queue Configurations"


class QueuedJob(models.Model):
    """Represents a job in the processing queue"""
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='queue_jobs')
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_for = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    priority_flag = models.BooleanField(default=False)
    worker_id = models.CharField(max_length=100, null=True, blank=True)
    
    def __str__(self):
        return f"Job {self.id} - {self.order.first_name} {self.order.surname} - {self.get_status_display()}"
    
    def mark_as_processing(self, worker_id=None):
        self.status = 'processing'
        self.started_at = timezone.now()
        self.worker_id = worker_id
        self.save()
    
    def mark_as_completed(self):
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()
    
    def mark_as_failed(self):
        self.status = 'failed'
        self.save()
    
    def requeue(self):
        self.status = 'queued'
        self.retry_count += 1
        self.save()
    
    def order_link(self):
        return f"{self.order.first_name} {self.order.surname}"
    order_link.short_description = "Customer"

    class Meta:
        ordering = ['-priority_flag', 'created_at']
        verbose_name = "Queued Job"
        verbose_name_plural = "Queued Jobs"


class JobError(models.Model):
    """Records errors that occur during job processing"""
    job = models.ForeignKey(QueuedJob, on_delete=models.CASCADE, related_name='errors')
    occurred_at = models.DateTimeField(auto_now_add=True)
    error_message = models.TextField()
    error_trace = models.TextField(blank=True)
    
    def __str__(self):
        return f"Error for Job {self.job.id} at {self.occurred_at}"

    class Meta:
        ordering = ['-occurred_at']
        verbose_name = "Job Error"
        verbose_name_plural = "Job Errors"




