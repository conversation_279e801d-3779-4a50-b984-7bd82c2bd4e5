from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time
import signal
import sys

class Command(BaseCommand):
    help = 'Start at least one worker for all location queues'

    def add_arguments(self, parser):
        parser.add_argument('--workers-per-location', type=int, default=1, help='Number of workers per location (default: 1)')
        parser.add_argument('--interval', type=int, default=30, help='Check interval in seconds (default: 30)')
        parser.add_argument('--max-jobs-per-cycle', type=int, default=5, help='Max jobs per cycle (default: 5)')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING WORKERS FOR ALL LOCATIONS"))
        self.stdout.write("=" * 70)
        
        # Get all locations
        locations = Location.objects.all()
        if not locations.exists():
            self.stdout.write(self.style.ERROR("❌ No locations found in database"))
            return
        
        workers_per_location = options['workers_per_location']
        interval = options['interval']
        max_jobs = options['max_jobs_per_cycle']
        
        self.stdout.write(f"📍 Found {locations.count()} locations")
        self.stdout.write(f"👥 Starting {workers_per_location} worker(s) per location")
        self.stdout.write(f"⏰ Check interval: {interval} seconds")
        self.stdout.write(f"🔧 Max jobs per cycle: {max_jobs}")
        self.stdout.write("=" * 70)
        
        workers_started = []
        
        # Start workers for each location
        for location in locations:
            self.stdout.write(f"\n📍 Starting workers for: {location.location_name}")
            
            for worker_num in range(workers_per_location):
                worker_name = f"worker_{location.location_name}_{worker_num + 1}"
                
                self.stdout.write(f"  → Starting {worker_name}")
                
                # Build the auto worker command
                cmd = [
                    'python', 'manage.py', 'auto_worker',
                    f'--interval={interval}',
                    f'--max-jobs-per-cycle={max_jobs}',
                    f'--location={location.location_name}',
                    f'--worker-name={worker_name}'
                ]
                
                try:
                    # Start the worker process
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )
                    
                    workers_started.append({
                        'name': worker_name,
                        'location': location.location_name,
                        'location_id': location.id,
                        'pid': process.pid,
                        'process': process,
                        'cmd': cmd
                    })
                    
                    self.stdout.write(f"    ✅ {worker_name} started (PID: {process.pid})")
                    
                    # Small delay between worker starts
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"    ❌ Failed to start {worker_name}: {e}"))
        
        # Summary
        self.stdout.write(f"\n" + "="*70)
        self.stdout.write(self.style.SUCCESS(f"🎉 WORKERS STARTED FOR ALL LOCATIONS"))
        self.stdout.write(f"Total workers started: {len(workers_started)}")
        
        # Group by location for display
        location_workers = {}
        for worker in workers_started:
            loc = worker['location']
            if loc not in location_workers:
                location_workers[loc] = []
            location_workers[loc].append(worker)
        
        for location, workers in location_workers.items():
            self.stdout.write(f"\n📍 {location}:")
            for worker in workers:
                self.stdout.write(f"  🔄 {worker['name']} (PID: {worker['pid']})")
        
        self.stdout.write(f"\n📋 How it works:")
        self.stdout.write(f"  • Each worker checks for jobs every {interval} seconds")
        self.stdout.write(f"  • Workers process up to {max_jobs} jobs per cycle")
        self.stdout.write(f"  • Workers handle retries and failures automatically")
        self.stdout.write(f"  • No Redis required - uses direct database processing")
        
        self.stdout.write(f"\n🔍 Monitoring:")
        self.stdout.write(f"  • Check processes: tasklist /FI \"IMAGENAME eq python.exe\"")
        self.stdout.write(f"  • Check jobs: python manage.py test_queue --list-jobs")
        self.stdout.write(f"  • Stop all workers: taskkill /F /IM python.exe")
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            self.stdout.write(f"\n\n🛑 STOPPING ALL WORKERS...")
            for worker in workers_started:
                try:
                    worker['process'].terminate()
                    self.stdout.write(f"  ✅ Stopped {worker['name']} (PID: {worker['pid']})")
                except:
                    pass
            self.stdout.write(f"✅ All workers stopped")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        self.stdout.write("="*70)
        self.stdout.write(f"\n👀 MONITORING WORKERS (Press Ctrl+C to stop all)")
        self.stdout.write("-" * 50)
        
        # Monitor workers and restart if needed
        try:
            check_count = 0
            while True:
                check_count += 1
                time.sleep(30)  # Check every 30 seconds
                
                self.stdout.write(f"\n[{time.strftime('%H:%M:%S')}] Check #{check_count}: Monitoring {len(workers_started)} workers...")
                
                running_count = 0
                stopped_workers = []
                
                for worker in workers_started:
                    if worker['process'].poll() is None:
                        running_count += 1
                    else:
                        stopped_workers.append(worker)
                
                if stopped_workers:
                    self.stdout.write(f"⚠️  {len(stopped_workers)} worker(s) stopped - restarting...")
                    
                    for worker in stopped_workers:
                        self.stdout.write(f"  🔄 Restarting {worker['name']} for {worker['location']}")
                        
                        try:
                            # Restart the worker
                            new_process = subprocess.Popen(
                                worker['cmd'],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                universal_newlines=True
                            )
                            worker['process'] = new_process
                            worker['pid'] = new_process.pid
                            self.stdout.write(f"    ✅ Restarted {worker['name']} (PID: {new_process.pid})")
                            running_count += 1
                        except Exception as e:
                            self.stdout.write(f"    ❌ Failed to restart {worker['name']}: {e}")
                
                if running_count == len(workers_started):
                    self.stdout.write(f"✅ All {running_count} workers running")
                elif running_count > 0:
                    self.stdout.write(f"⚠️  {running_count}/{len(workers_started)} workers running")
                else:
                    self.stdout.write(self.style.ERROR("❌ No workers running - attempting restart..."))
                    
        except KeyboardInterrupt:
            self.stdout.write(f"\n\n🛑 Shutdown requested by user")
            signal_handler(None, None)
