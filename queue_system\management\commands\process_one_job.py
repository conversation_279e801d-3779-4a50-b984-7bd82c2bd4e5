from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order

class Command(BaseCommand):
    help = 'Process one queued job'

    def handle(self, *args, **options):
        # Get the next queued job
        job = QueuedJob.objects.filter(status='queued').order_by('created_at').first()
        
        if not job:
            self.stdout.write("No queued jobs found")
            return
        
        self.stdout.write(f"Processing job {job.id} for order {job.order.id}")
        self.stdout.write(f"Customer: {job.order.first_name} {job.order.surname}")
        
        try:
            # Process the job directly
            result = process_order(str(job.order.id))
            
            if result:
                self.stdout.write(self.style.SUCCESS(f"Job {job.id} completed successfully"))
            else:
                self.stdout.write(self.style.ERROR(f"Job {job.id} failed"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error processing job {job.id}: {str(e)}"))
            import traceback
            self.stdout.write(traceback.format_exc())
