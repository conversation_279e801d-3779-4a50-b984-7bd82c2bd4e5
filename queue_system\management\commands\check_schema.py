from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Check database schema for queue_system tables'

    def handle(self, *args, **options):
        self.stdout.write("Checking database schema...")
        
        # Check tables
        tables_to_check = [
            'queue_system_queuedjob',
            'queue_system_joberror',
            'queue_system_locationqueueconfig'
        ]
        
        with connection.cursor() as cursor:
            for table in tables_to_check:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if cursor.fetchone():
                    self.stdout.write(self.style.SUCCESS(f"Table {table} exists"))
                    
                    # Check columns
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    self.stdout.write(f"Columns in {table}:")
                    for column in columns:
                        self.stdout.write(f"  - {column[0]}: {column[1]}")
                else:
                    self.stdout.write(self.style.ERROR(f"Table {table} does not exist"))
        
        self.stdout.write(self.style.SUCCESS("Schema check complete"))