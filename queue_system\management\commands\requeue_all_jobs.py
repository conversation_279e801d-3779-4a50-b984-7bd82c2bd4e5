from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
from orders.models import order as Order  # Fix the import with alias

class Command(BaseCommand):
    help = 'Requeue all jobs in queued status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--status',
            type=str,
            default='queued',
            help='Status of jobs to requeue (default: queued)',
        )
        
        parser.add_argument(
            '--location',
            type=str,
            help='Requeue jobs only for a specific location (by name)',
        )

    def handle(self, *args, **options):
        status = options['status']
        location = options['location']
        
        # Get jobs to requeue
        jobs = QueuedJob.objects.filter(status=status)
        
        if location:
            jobs = jobs.filter(location__location_name__icontains=location)
        
        if not jobs.exists():
            self.stdout.write(self.style.WARNING(f"No jobs found with status: {status}"))
            return
        
        self.stdout.write(f"Found {jobs.count()} jobs to requeue")
        
        # Requeue each job
        for job in jobs:
            order_id = job.order.id
            location_id = job.location.id
            
            self.stdout.write(f"Requeuing job {job.id} for order {order_id} to queue: location.{location_id}")
            
            # Use apply_async with explicit queue name
            task = process_order.apply_async(
                args=[str(order_id)],
                queue=f'location.{location_id}'
            )
            
            self.stdout.write(f"Job {job.id} requeued with task ID: {task.id}")
        
        self.stdout.write(self.style.SUCCESS(f"Requeued {jobs.count()} jobs"))
