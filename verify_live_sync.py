#!/usr/bin/env python
"""
Verify live sync is working
"""

import os
import sys
import time
import requests

# Setup Django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()

from queue_system.tasks import sync_worker_status_task

def get_db_worker_count():
    """Get current worker count from database via API"""
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data['overview']['total_workers']
        else:
            return f"API Error: {response.status_code}"
    except Exception as e:
        return f"Error: {e}"

def main():
    print("🔍 VERIFYING LIVE SYNC FUNCTIONALITY")
    print("=" * 50)
    
    # Get initial state
    print("1. Initial database state:")
    initial_count = get_db_worker_count()
    print(f"   Database workers: {initial_count}")
    
    # Run sync task manually
    print("\n2. Running sync task manually:")
    try:
        result = sync_worker_status_task()
        print(f"   ✅ Sync result: {result}")
    except Exception as e:
        print(f"   ❌ Sync failed: {e}")
        return
    
    # Check database after sync
    print("\n3. Database state after manual sync:")
    after_sync_count = get_db_worker_count()
    print(f"   Database workers: {after_sync_count}")
    
    # Check if sync changed anything
    if initial_count != after_sync_count:
        print(f"   🔄 SYNC DETECTED: {initial_count} → {after_sync_count}")
    else:
        print(f"   📊 No change needed (already in sync)")
    
    # Test automatic sync by waiting
    print("\n4. Testing automatic sync (waiting 15 seconds):")
    print("   Watching for automatic database updates...")
    
    last_count = after_sync_count
    for i in range(3):  # Check 3 times over 15 seconds
        time.sleep(5)
        current_count = get_db_worker_count()
        elapsed = (i + 1) * 5
        
        if current_count != last_count:
            print(f"   🔄 AUTO SYNC at {elapsed}s: {last_count} → {current_count}")
        else:
            print(f"   📊 {elapsed}s: {current_count} (no change)")
        
        last_count = current_count
    
    print(f"\n✅ VERIFICATION COMPLETE")
    print(f"   • Manual sync: Working")
    print(f"   • Database updates: Working") 
    print(f"   • Final worker count: {last_count}")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   • Check admin UI: http://localhost:8000/queue/admin/queue-overview/")
    print(f"   • UI should update every 5 seconds automatically")
    print(f"   • Worker counts should match Celery reality")

if __name__ == '__main__':
    main()
