#!/bin/bash

# Check if columns exist
echo "Checking if columns already exist..."
python -c "
from django.db import connection

with connection.cursor() as cursor:
    try:
        cursor.execute(\"DESCRIBE queue_system_locationqueueconfig\")
        columns = cursor.fetchall()
        
        column_names = [col[0] for col in columns]
        print(\"Existing columns in queue_system_locationqueueconfig:\")
        for name in column_names:
            print(f\"  - {name}\")
            
        # Check if auto_scale and min_workers exist
        has_auto_scale = 'auto_scale' in column_names
        has_min_workers = 'min_workers' in column_names
        
        print(f\"auto_scale column exists: {has_auto_scale}\")
        print(f\"min_workers column exists: {has_min_workers}\")
        
        if has_auto_scale and has_min_workers:
            print(\"Both columns already exist, need to fake the migration\")
            with open('migration_action.txt', 'w') as f:
                f.write('fake')
        else:
            print(\"At least one column is missing, migration might be needed\")
            with open('migration_action.txt', 'w') as f:
                f.write('real')
    except Exception as e:
        print(f\"Error checking columns: {e}\")
        with open('migration_action.txt', 'w') as f:
            f.write('error')
"

# Read the migration action
MIGRATION_ACTION=$(cat migration_action.txt)
rm migration_action.txt

if [ "$MIGRATION_ACTION" = "fake" ]; then
    echo "Creating a fake migration file..."
    cat > queue_system/migrations/0003_fake_auto_scale_fields.py << EOF
from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('queue_system', '0002_auto_check_existing_tables'),
    ]

    operations = [
        # This is an empty migration that pretends to add the auto_scale and min_workers fields
        # The fields already exist in the database, so we don't need to add them again
    ]
EOF

    # Remove the conflicting migrations
    echo "Removing conflicting migrations..."
    rm -f queue_system/migrations/add_auto_scale_fields.py
    rm -f queue_system/migrations/0003_add_auto_scale_fields.py

    # Update the migration record in the database
    echo "Updating migration records in the database..."
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    # Check if the add_auto_scale_fields record exists
    cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'\")
    migration = cursor.fetchone()
    if migration:
        print('Migration record for add_auto_scale_fields exists, updating it...')
        cursor.execute(\"UPDATE django_migrations SET name='0003_fake_auto_scale_fields' WHERE app='queue_system' AND name='add_auto_scale_fields'\")
        print('Migration record updated')
    else:
        # Check if the 0003_add_auto_scale_fields record exists
        cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
        migration = cursor.fetchone()
        if migration:
            print('Migration record for 0003_add_auto_scale_fields exists, updating it...')
            cursor.execute(\"UPDATE django_migrations SET name='0003_fake_auto_scale_fields' WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
            print('Migration record updated')
        else:
            print('No migration records found, creating one...')
            cursor.execute(\"INSERT INTO django_migrations (app, name, applied) VALUES ('queue_system', '0003_fake_auto_scale_fields', NOW())\")
            print('Migration record created')
"
elif [ "$MIGRATION_ACTION" = "real" ]; then
    echo "Creating a real migration file..."
    cat > queue_system/migrations/0003_add_missing_fields.py << EOF
from django.db import migrations, models

class Migration(migrations.Migration):

    dependencies = [
        ('queue_system', '0002_auto_check_existing_tables'),
    ]

    operations = [
        migrations.AddField(
            model_name='locationqueueconfig',
            name='auto_scale',
            field=models.BooleanField(default=True, help_text='Automatically scale workers based on queue length'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='locationqueueconfig',
            name='min_workers',
            field=models.PositiveIntegerField(default=0, help_text='Minimum number of workers to keep running'),
            preserve_default=False,
        ),
    ]
EOF

    # Remove the conflicting migrations
    echo "Removing conflicting migrations..."
    rm -f queue_system/migrations/add_auto_scale_fields.py
    rm -f queue_system/migrations/0003_add_auto_scale_fields.py

    # Update the migration record in the database
    echo "Updating migration records in the database..."
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    # Check if the add_auto_scale_fields record exists
    cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'\")
    migration = cursor.fetchone()
    if migration:
        print('Migration record for add_auto_scale_fields exists, updating it...')
        cursor.execute(\"UPDATE django_migrations SET name='0003_add_missing_fields' WHERE app='queue_system' AND name='add_auto_scale_fields'\")
        print('Migration record updated')
    else:
        # Check if the 0003_add_auto_scale_fields record exists
        cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
        migration = cursor.fetchone()
        if migration:
            print('Migration record for 0003_add_auto_scale_fields exists, updating it...')
            cursor.execute(\"UPDATE django_migrations SET name='0003_add_missing_fields' WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
            print('Migration record updated')
        else:
            print('No migration records found, will be created during migration')
    "
else
    echo "Error checking columns, trying to fix manually..."
    
    # Try to add columns if they don't exist
    echo "Attempting to add columns if they don't exist..."
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    try:
        # Check if auto_scale column exists
        cursor.execute(\"SHOW COLUMNS FROM queue_system_locationqueueconfig LIKE 'auto_scale'\")
        if not cursor.fetchone():
            print('Adding auto_scale column...')
            cursor.execute(\"ALTER TABLE queue_system_locationqueueconfig ADD COLUMN auto_scale BOOLEAN DEFAULT TRUE\")
            print('auto_scale column added')
        else:
            print('auto_scale column already exists')
            
        # Check if min_workers column exists
        cursor.execute(\"SHOW COLUMNS FROM queue_system_locationqueueconfig LIKE 'min_workers'\")
        if not cursor.fetchone():
            print('Adding min_workers column...')
            cursor.execute(\"ALTER TABLE queue_system_locationqueueconfig ADD COLUMN min_workers INTEGER DEFAULT 0\")
            print('min_workers column added')
        else:
            print('min_workers column already exists')
    except Exception as e:
        print(f'Error adding columns: {e}')
"
    
    # Create a fake migration file
    echo "Creating a fake migration file..."
    cat > queue_system/migrations/0003_fake_auto_scale_fields.py << EOF
from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('queue_system', '0002_auto_check_existing_tables'),
    ]

    operations = [
        # This is an empty migration that pretends to add the auto_scale and min_workers fields
        # We've already added them manually if they didn't exist
    ]
EOF

    # Remove the conflicting migrations
    echo "Removing conflicting migrations..."
    rm -f queue_system/migrations/add_auto_scale_fields.py
    rm -f queue_system/migrations/0003_add_auto_scale_fields.py

    # Update the migration record in the database
    echo "Updating migration records in the database..."
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    # Check if the add_auto_scale_fields record exists
    cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'\")
    migration = cursor.fetchone()
    if migration:
        print('Migration record for add_auto_scale_fields exists, updating it...')
        cursor.execute(\"UPDATE django_migrations SET name='0003_fake_auto_scale_fields' WHERE app='queue_system' AND name='add_auto_scale_fields'\")
        print('Migration record updated')
    else:
        # Check if the 0003_add_auto_scale_fields record exists
        cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
        migration = cursor.fetchone()
        if migration:
            print('Migration record for 0003_add_auto_scale_fields exists, updating it...')
            cursor.execute(\"UPDATE django_migrations SET name='0003_fake_auto_scale_fields' WHERE app='queue_system' AND name='0003_add_auto_scale_fields'\")
            print('Migration record updated')
        else:
            print('No migration records found, creating one...')
            cursor.execute(\"INSERT INTO django_migrations (app, name, applied) VALUES ('queue_system', '0003_fake_auto_scale_fields', NOW())\")
            print('Migration record created')
"
fi

# Show migration status
echo "Migration status after fix:"
python manage.py showmigrations queue_system

# Try to apply migrations
echo "Applying migrations..."
python manage.py migrate queue_system

# Check schema
echo "Checking database schema..."
python manage.py check_schema

echo "Fix complete"