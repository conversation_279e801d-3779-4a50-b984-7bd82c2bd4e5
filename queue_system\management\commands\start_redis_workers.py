from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time

class Command(BaseCommand):
    help = 'Start Redis-based Celery workers for each location queue'

    def add_arguments(self, parser):
        parser.add_argument('--location', type=str, help='Start worker for specific location only')
        parser.add_argument('--concurrency', type=int, default=1, help='Number of worker processes per location')
        parser.add_argument('--beat', action='store_true', help='Start Celery beat scheduler')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting Redis-based Celery workers"))
        
        # Get locations
        if options['location']:
            locations = Location.objects.filter(location_name__icontains=options['location'])
        else:
            locations = Location.objects.all()
        
        if not locations.exists():
            self.stdout.write(self.style.ERROR("No locations found"))
            return
        
        concurrency = options['concurrency']
        workers_started = []
        
        # Start worker for each location
        for location in locations:
            queue_name = f'location.{location.id}'
            worker_name = f'worker_{location.location_name}_{location.id}'
            
            self.stdout.write(f"Starting worker for {location.location_name} (Queue: {queue_name})")
            
            # Build the celery worker command
            cmd = [
                'celery', '-A', 'config', 'worker',
                '--loglevel=info',
                f'--concurrency={concurrency}',
                f'--queues={queue_name}',
                f'--hostname={worker_name}@%h',
                '--without-gossip',
                '--without-mingle',
                '--without-heartbeat'
            ]
            
            try:
                # Start the worker process
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'location': location.location_name,
                    'queue': queue_name,
                    'pid': process.pid,
                    'process': process
                })
                
                self.stdout.write(f"Worker started with PID: {process.pid}")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Failed to start worker for {location.location_name}: {e}"))
        
        # Start scheduler worker if requested
        if options['beat']:
            self.stdout.write("Starting Celery Beat scheduler")
            
            beat_cmd = [
                'celery', '-A', 'config', 'beat',
                '--loglevel=info',
                '--scheduler=django_celery_beat.schedulers:DatabaseScheduler'
            ]
            
            try:
                beat_process = subprocess.Popen(
                    beat_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'location': 'scheduler',
                    'queue': 'beat',
                    'pid': beat_process.pid,
                    'process': beat_process
                })
                
                self.stdout.write(f"Celery Beat started with PID: {beat_process.pid}")
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Failed to start Celery Beat: {e}"))
        
        # Summary
        self.stdout.write(self.style.SUCCESS(f"\nStarted {len(workers_started)} workers:"))
        for worker in workers_started:
            self.stdout.write(f"  - {worker['location']}: PID {worker['pid']} (Queue: {worker['queue']})")
        
        self.stdout.write(self.style.SUCCESS("\nWorkers are running in the background."))
        self.stdout.write("Use 'celery -A config inspect active_queues' to check worker status")
        self.stdout.write("Use 'tasklist /FI \"IMAGENAME eq python.exe\"' to see running processes")
