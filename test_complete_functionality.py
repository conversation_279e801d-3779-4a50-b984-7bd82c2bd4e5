#!/usr/bin/env python
"""
Complete test of worker management and live updates functionality
"""

import requests
import json
import time
import subprocess
import os
import sys

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, '.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    import django
    django.setup()

def test_api_functionality():
    """Test all API endpoints"""
    print("🧪 TESTING API FUNCTIONALITY")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test overview API
    print("\n1. Testing Overview API...")
    response = requests.get(f"{base_url}/queue/api/live-stats/")
    if response.status_code == 200:
        data = response.json()
        overview = data['overview']
        print(f"   ✅ Total Workers: {overview['total_workers']}/{overview['total_max_workers']}")
        print(f"   ✅ Total Queued: {overview['total_queued']}")
        print(f"   ✅ Locations: {len(overview['locations'])}")
    else:
        print(f"   ❌ API Error: {response.status_code}")
    
    # Test location API
    setup_django()
    from locations.models import Location
    location = Location.objects.first()
    
    print(f"\n2. Testing Location API for {location.location_name}...")
    response = requests.get(f"{base_url}/queue/api/live-stats/{location.id}/")
    if response.status_code == 200:
        data = response.json()
        loc_data = data['location']
        print(f"   ✅ Workers: {loc_data['active_workers']}/{loc_data['max_workers']}")
        print(f"   ✅ Jobs: Q:{loc_data['queued']} P:{loc_data['processing']} R:{loc_data['review']}")
        return location
    else:
        print(f"   ❌ API Error: {response.status_code}")
        return None

def test_worker_lifecycle():
    """Test complete worker lifecycle"""
    print("\n🔄 TESTING WORKER LIFECYCLE")
    print("=" * 50)
    
    # Step 1: Verify clean state
    print("\n1. Verifying clean state...")
    result = subprocess.run(['python', 'manage.py', 'sync_worker_status'], 
                          capture_output=True, text=True)
    if "Updated: 0 locations" in result.stdout:
        print("   ✅ All workers at 0 (clean state)")
    else:
        print("   ⚠️ Some workers were reset to 0")
    
    # Step 2: Start a test worker
    print("\n2. Starting test worker...")
    try:
        worker_process = subprocess.Popen([
            'celery', '-A', 'config', 'worker',
            '--loglevel=info',
            '--concurrency=1',
            '--queues=location.744ae77f-8a7c-41d1-b9c8-00dd0fe020dd',
            '--hostname=test_worker@%h'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for worker to start
        time.sleep(3)
        
        # Check if worker is running
        tasklist_result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                                       capture_output=True, text=True)
        if 'celery.exe' in tasklist_result.stdout:
            print("   ✅ Celery worker process started")
        else:
            print("   ❌ Celery worker process not found")
            return False
        
        # Step 3: Sync and verify detection
        print("\n3. Syncing worker status...")
        sync_result = subprocess.run(['python', 'manage.py', 'sync_worker_status'], 
                                   capture_output=True, text=True)
        if "Updated: 1 locations" in sync_result.stdout:
            print("   ✅ Worker detected and database updated")
        else:
            print("   ❌ Worker not detected properly")
            print(f"   Debug: {sync_result.stdout}")
        
        # Step 4: Test API reflects changes
        print("\n4. Testing API reflects worker...")
        response = requests.get('http://localhost:8000/queue/api/live-stats/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/')
        if response.status_code == 200:
            data = response.json()
            workers = data['location']['active_workers']
            if workers > 0:
                print(f"   ✅ API shows {workers} active worker(s)")
            else:
                print("   ❌ API still shows 0 workers")
        
        # Step 5: Stop worker
        print("\n5. Stopping worker...")
        worker_process.terminate()
        worker_process.wait()
        
        # Kill any remaining celery processes
        subprocess.run(['taskkill', '/F', '/IM', 'celery.exe'], 
                      capture_output=True, text=True)
        
        time.sleep(2)
        
        # Step 6: Sync and verify cleanup
        print("\n6. Syncing after worker stop...")
        sync_result = subprocess.run(['python', 'manage.py', 'sync_worker_status'], 
                                   capture_output=True, text=True)
        if "Updated: 1 locations" in sync_result.stdout:
            print("   ✅ Worker removal detected and database updated")
        else:
            print("   ⚠️ No changes detected (may already be 0)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error in worker lifecycle test: {e}")
        return False

def test_live_updates():
    """Test live update functionality"""
    print("\n📡 TESTING LIVE UPDATES")
    print("=" * 50)
    
    print("\n1. Testing live update endpoints...")
    
    # Test overview endpoint
    response = requests.get('http://localhost:8000/queue/api/live-stats/')
    if response.status_code == 200:
        print("   ✅ Overview live stats API working")
    else:
        print(f"   ❌ Overview API error: {response.status_code}")
    
    # Test location endpoint
    response = requests.get('http://localhost:8000/queue/api/live-stats/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/')
    if response.status_code == 200:
        print("   ✅ Location live stats API working")
    else:
        print(f"   ❌ Location API error: {response.status_code}")
    
    print("\n2. Manual testing instructions:")
    print("   • Open: http://localhost:8000/queue/admin/queue-overview/")
    print("   • Open: http://localhost:8000/queue/admin/location/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/")
    print("   • Watch for live updates every 10-15 seconds")
    print("   • Look for 'Live' indicator in overview page")

def main():
    """Run all tests"""
    print("🚀 COMPREHENSIVE WORKER MANAGEMENT & LIVE UPDATES TEST")
    print("=" * 70)
    
    try:
        # Test API functionality
        location = test_api_functionality()
        
        # Test worker lifecycle
        worker_test_passed = test_worker_lifecycle()
        
        # Test live updates
        test_live_updates()
        
        # Summary
        print("\n" + "=" * 70)
        print("📋 TEST SUMMARY")
        print("=" * 70)
        
        if worker_test_passed:
            print("✅ Worker Management: PASSED")
            print("   • Workers can be started and detected")
            print("   • Database syncs with actual worker status")
            print("   • API reflects real-time worker counts")
            print("   • Worker cleanup works properly")
        else:
            print("❌ Worker Management: FAILED")
        
        print("\n✅ Live Updates: AVAILABLE")
        print("   • Overview API: /queue/api/live-stats/")
        print("   • Location API: /queue/api/live-stats/{location_id}/")
        print("   • Admin UI updates every 10-15 seconds")
        
        print("\n🎯 NEXT STEPS:")
        print("   1. Open admin UI and test worker increase/decrease buttons")
        print("   2. Watch for live updates without page refresh")
        print("   3. Start production workers: python manage.py start_production_workers")
        print("   4. Use management menu: .\\manage_workers.bat")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")

if __name__ == '__main__':
    main()
