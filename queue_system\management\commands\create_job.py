from django.core.management.base import BaseCommand
from orders.models import order
from queue_system.tasks import schedule_job
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a queue job for an order'

    def add_arguments(self, parser):
        parser.add_argument('order_id', type=str, help='ID of the order to create a job for')

    def handle(self, *args, **options):
        order_id = options['order_id']
        self.stdout.write(f"Creating job for order {order_id}")
        
        try:
            # Get the order
            order_obj = order.objects.get(id=order_id)
            self.stdout.write(f"Found order: {order_obj.first_name} {order_obj.surname}")
            
            # Create the job
            job_id = schedule_job(order_id)
            
            if job_id:
                self.stdout.write(self.style.SUCCESS(f"Successfully created job {job_id} for order {order_id}"))
            else:
                self.stdout.write(self.style.ERROR(f"Failed to create job for order {order_id}"))
        except order.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Order {order_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating job: {str(e)}"))