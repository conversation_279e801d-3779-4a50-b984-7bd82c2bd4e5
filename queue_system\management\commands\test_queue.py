from django.core.management.base import BaseCommand
from orders.models import order
from queue_system.models import QueuedJob
from queue_system.tasks import schedule_job
import uuid

class Command(BaseCommand):
    help = 'Test the queue system'

    def add_arguments(self, parser):
        parser.add_argument('--create-test-order', action='store_true', help='Create a test order')
        parser.add_argument('--test-signal', action='store_true', help='Test the signal by updating an order status')
        parser.add_argument('--list-orders', action='store_true', help='List all orders')
        parser.add_argument('--list-jobs', action='store_true', help='List all jobs')

    def handle(self, *args, **options):
        if options['list_orders']:
            self.list_orders()
        
        if options['list_jobs']:
            self.list_jobs()
            
        if options['create_test_order']:
            self.create_test_order()
            
        if options['test_signal']:
            self.test_signal()

    def list_orders(self):
        orders = order.objects.all()
        self.stdout.write(f"Total orders: {orders.count()}")
        
        for o in orders[:5]:
            self.stdout.write(f"Order {o.id}: {o.first_name} {o.surname} - Status: {o.status}")

    def list_jobs(self):
        jobs = QueuedJob.objects.all()
        self.stdout.write(f"Total jobs: {jobs.count()}")
        
        for job in jobs[:5]:
            self.stdout.write(f"Job {job.id}: Order {job.order.id} - Status: {job.status}")

    def create_test_order(self):
        from locations.models import Location
        
        # Get the first location
        location = Location.objects.first()
        if not location:
            self.stdout.write(self.style.ERROR("No locations found. Please create a location first."))
            return
            
        # Create a test order
        test_order = order.objects.create(
            first_name="Test",
            surname="User",
            customer_email="<EMAIL>",
            location=location,
            status='pending'
        )
        
        self.stdout.write(self.style.SUCCESS(f"Created test order: {test_order.id}"))
        return test_order

    def test_signal(self):
        # Get the first order or create one
        test_order = order.objects.first()
        if not test_order:
            test_order = self.create_test_order()
            
        if not test_order:
            return
            
        self.stdout.write(f"Testing signal with order {test_order.id}")
        
        # Update the order status to trigger the signal
        test_order.status = 'criminal_check_passed'
        test_order.save()
        
        self.stdout.write(self.style.SUCCESS(f"Updated order {test_order.id} status to 'criminal_check_passed'"))
        
        # Check if a job was created
        jobs = QueuedJob.objects.filter(order=test_order)
        if jobs.exists():
            job = jobs.first()
            self.stdout.write(self.style.SUCCESS(f"Job created: {job.id} with status: {job.status}"))
        else:
            self.stdout.write(self.style.ERROR("No job was created"))
