#!/bin/bash

# Start the Celery workers for all locations, respecting the configuration in the database
python manage.py start_queue_workers --init-queues --scheduler --beat --respect-config --debug

# Or for a specific location:
# python manage.py start_queue_workers --location=Barbados --scheduler --beat --respect-config --debug

# Or start the worker manager daemon:
# python manage.py manage_workers --interval=30 --debug

