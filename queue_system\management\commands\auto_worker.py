from django.core.management.base import BaseCommand
from queue_system.models import QueuedJob
from queue_system.tasks import process_order
import time
import logging
import sys

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Run an automatic worker that continuously polls for queued jobs'

    def add_arguments(self, parser):
        parser.add_argument('--interval', type=int, default=10, help='Polling interval in seconds (default: 10)')
        parser.add_argument('--max-jobs-per-cycle', type=int, default=3, help='Max jobs to process per cycle (default: 3)')
        parser.add_argument('--location', type=str, help='Only process jobs for specific location')
        parser.add_argument('--worker-name', type=str, default='auto_worker', help='Name for this worker instance')

    def handle(self, *args, **options):
        interval = options['interval']
        max_jobs = options['max_jobs_per_cycle']
        location_filter = options.get('location')
        worker_name = options.get('worker_name', 'auto_worker')

        # Force output to be unbuffered
        sys.stdout.flush()
        sys.stderr.flush()

        print(f"🚀 Starting {worker_name}", flush=True)
        print(f"Polling interval: {interval} seconds", flush=True)
        print(f"Max jobs per cycle: {max_jobs}", flush=True)
        if location_filter:
            print(f"Location filter: {location_filter}", flush=True)

        print("Press Ctrl+C to stop", flush=True)
        print("=" * 50, flush=True)

        try:
            cycle_count = 0
            while True:
                cycle_count += 1
                print(f"\n[Cycle {cycle_count}] Checking for queued jobs...", flush=True)

                processed_count = self.process_cycle(max_jobs, location_filter)

                if processed_count > 0:
                    print(f"✓ Processed {processed_count} jobs", flush=True)
                else:
                    print("• No queued jobs found", flush=True)

                print(f"Waiting {interval} seconds...", flush=True)
                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n\nWorker stopped by user")

    def process_cycle(self, max_jobs, location_filter):
        """Process one cycle of queued jobs"""
        # Get queued jobs
        jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')
        
        if location_filter:
            jobs = jobs.filter(location__location_name__icontains=location_filter)
        
        if not jobs.exists():
            return 0
        
        processed_count = 0
        for job in jobs[:max_jobs]:
            print(f"  → Processing job {job.id} for order {job.order.id} ({job.order.first_name} {job.order.surname})", flush=True)

            try:
                # Process the job directly
                result = process_order(str(job.order.id))

                if result:
                    print(f"    ✓ Job {job.id} completed successfully", flush=True)
                else:
                    print(f"    ✗ Job {job.id} failed", flush=True)

                processed_count += 1

            except Exception as e:
                print(f"    ✗ Error processing job {job.id}: {str(e)}", flush=True)
                processed_count += 1
        
        return processed_count
