#!/bin/bash

# Check migration status
echo "Current migration status:"
python manage.py showmigrations queue_system

# Check for conflicting migrations
echo "Checking for conflicting migrations..."
python -c "
from django.db.migrations.loader import MigrationLoader
from django.db import connections
loader = MigrationLoader(connections['default'])
conflicts = loader.detect_conflicts()
if 'queue_system' in conflicts:
    print('Conflicts detected in queue_system app')
    for conflict in conflicts['queue_system']:
        print(f'  - {conflict}')
else:
    print('No conflicts detected')
"

# Try to merge migrations if conflicts exist
echo "Attempting to merge conflicting migrations..."
python manage.py makemigrations --merge

# If merge fails, manually fix the issue
if [ $? -ne 0 ]; then
    echo "Merge failed, manually fixing the issue..."
    
    # Remove the non-numbered migration file
    echo "Removing add_auto_scale_fields.py..."
    rm -f queue_system/migrations/add_auto_scale_fields.py
    
    # Update the migration record in the database
    echo "Updating migration record in the database..."
    python -c "
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute(\"SELECT * FROM django_migrations WHERE app='queue_system' AND name='add_auto_scale_fields'\")
        migration = cursor.fetchone()
        if migration:
            print('Migration record exists, updating it...')
            cursor.execute(\"UPDATE django_migrations SET name='0003_add_auto_scale_fields' WHERE app='queue_system' AND name='add_auto_scale_fields'\")
            print('Migration record updated')
        else:
            print('Migration record not found')
    "
fi

# Check migration status after fix
echo "Migration status after fix:"
python manage.py showmigrations queue_system

# Apply migrations
echo "Applying migrations..."
python manage.py migrate queue_system

# Check schema
echo "Checking database schema..."
python manage.py check_schema

# Squash migrations if needed
read -p "Do you want to squash migrations? (y/n) " answer
if [ "$answer" = "y" ]; then
    echo "Squashing migrations..."
    python manage.py squash_migrations --name initial
fi

echo "Migration fix process complete"