#!/usr/bin/env python
"""
Test the fixed live sync every 5 seconds
"""

import requests
import time
import subprocess
import os
import sys

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, '.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    import django
    django.setup()

def get_current_status():
    """Get current worker status from all sources"""
    
    # Tasklist count
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True)
        tasklist_count = len([line for line in result.stdout.split('\n') if 'celery.exe' in line])
    except:
        tasklist_count = 0
    
    # Database count via API
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            db_count = data['overview']['total_workers']
        else:
            db_count = "ERROR"
    except:
        db_count = "ERROR"
    
    # Test sync task manually
    try:
        setup_django()
        from queue_system.tasks import sync_worker_status_task
        result = sync_worker_status_task()
        sync_result = result
    except Exception as e:
        sync_result = f"ERROR: {e}"
    
    return tasklist_count, db_count, sync_result

def monitor_live_sync():
    """Monitor live sync in real-time"""
    print("🚀 FIXED LIVE DATABASE SYNC DEMONSTRATION")
    print("=" * 70)
    print("Monitoring database sync every 5 seconds...")
    print("(The new sync task should work without hanging)")
    print()
    print("Time | Processes | Database | Sync Task Result")
    print("-" * 60)
    
    start_time = time.time()
    last_db_count = None
    sync_count = 0
    
    try:
        while True:
            elapsed = int(time.time() - start_time)
            tasklist_count, db_count, sync_result = get_current_status()
            
            # Detect sync
            status = "📊"
            if last_db_count is not None and last_db_count != db_count:
                status = "🔄 SYNC!"
                sync_count += 1
            
            # Truncate sync result for display
            sync_display = str(sync_result)[:30] + "..." if len(str(sync_result)) > 30 else str(sync_result)
            
            print(f"{elapsed:3d}s | {tasklist_count:9d} | {str(db_count):8s} | {sync_display} {status}")
            
            last_db_count = db_count
            time.sleep(5)  # Check every 5 seconds to match sync interval
            
    except KeyboardInterrupt:
        print(f"\n" + "=" * 60)
        print(f"📊 MONITORING SUMMARY")
        print(f"   • Duration: {elapsed} seconds")
        print(f"   • Syncs detected: {sync_count}")
        print(f"   • Final status: {tasklist_count} processes, {db_count} in database")

def test_sync_task():
    """Test the sync task directly"""
    print("\n🔧 TESTING FIXED SYNC TASK")
    print("=" * 50)
    
    try:
        setup_django()
        from queue_system.tasks import sync_worker_status_task
        
        print("1. Testing sync task directly...")
        start_time = time.time()
        result = sync_worker_status_task()
        duration = (time.time() - start_time) * 1000
        
        print(f"   ✅ Sync completed in {duration:.1f}ms")
        print(f"   📊 Result: {result}")
        
    except Exception as e:
        print(f"   ❌ Sync task failed: {e}")
    
    # Test API response
    print("2. Testing API response...")
    try:
        start_time = time.time()
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        response_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API response: {response_time:.1f}ms")
            print(f"   📊 Total workers: {data['overview']['total_workers']}")
        else:
            print(f"   ❌ API error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API error: {e}")

def main():
    """Main demonstration"""
    print("🎯 FIXED LIVE DATABASE SYNC EVERY 5 SECONDS")
    print("=" * 70)
    
    # Show initial status
    print("📊 INITIAL STATUS")
    print("=" * 50)
    tasklist_count, db_count, sync_result = get_current_status()
    print(f"Tasklist processes: {tasklist_count}")
    print(f"Database workers: {db_count}")
    print(f"Sync task result: {sync_result}")
    
    # Test sync task
    test_sync_task()
    
    print(f"\n🎯 FIXES IMPLEMENTED:")
    print("   ✅ Removed hanging silent mode")
    print("   ✅ Added timeout to Celery inspect (3 seconds)")
    print("   ✅ Direct database updates (no command calls)")
    print("   ✅ Better error handling")
    print("   ✅ Faster execution")
    
    print(f"\n📱 ADMIN UI TESTING:")
    print("   • Overview: http://localhost:8000/queue/admin/queue-overview/")
    print("   • Should update every 5 seconds automatically")
    print("   • Worker counts should match reality")
    
    print(f"\n🔄 CURRENT COMPONENTS:")
    print("   • Celery Beat: Scheduling sync every 5 seconds")
    print("   • Scheduler Worker: Processing sync tasks")
    print("   • Test Workers: 2 location workers running")
    print("   • Django Server: Serving admin UI")
    
    # Start monitoring
    print(f"\n📡 STARTING LIVE MONITORING")
    print("Press Ctrl+C to stop monitoring")
    print("=" * 50)
    
    monitor_live_sync()

if __name__ == '__main__':
    main()
