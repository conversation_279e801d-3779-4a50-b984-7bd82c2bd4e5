#!/usr/bin/env python
"""
Demonstrate live database sync every 5 seconds
"""

import requests
import time
import subprocess
import os
import sys

def setup_django():
    """Setup Django environment"""
    sys.path.insert(0, '.')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    import django
    django.setup()

def get_current_status():
    """Get current worker status from all sources"""
    
    # Tasklist count
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'], 
                              capture_output=True, text=True)
        tasklist_count = len([line for line in result.stdout.split('\n') if 'celery.exe' in line])
    except:
        tasklist_count = 0
    
    # Celery inspect count
    try:
        setup_django()
        from config.celery import app
        inspect = app.control.inspect()
        workers = inspect.active()
        celery_count = len([w for w in workers.keys() if workers[w] is not None]) if workers else 0
    except:
        celery_count = 0
    
    # Database count via API
    try:
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            db_count = data['overview']['total_workers']
        else:
            db_count = "ERROR"
    except:
        db_count = "ERROR"
    
    return tasklist_count, celery_count, db_count

def monitor_live_sync():
    """Monitor live sync in real-time"""
    print("🚀 LIVE DATABASE SYNC DEMONSTRATION")
    print("=" * 70)
    print("Monitoring database sync every 5 seconds...")
    print("(Start/stop workers in another terminal to see live updates)")
    print()
    print("Time | Tasklist | Celery | Database | Status")
    print("-" * 50)
    
    start_time = time.time()
    last_db_count = None
    sync_count = 0
    
    try:
        while True:
            elapsed = int(time.time() - start_time)
            tasklist_count, celery_count, db_count = get_current_status()
            
            # Detect sync
            status = "📊"
            if last_db_count is not None and last_db_count != db_count:
                status = "🔄 SYNC!"
                sync_count += 1
            
            print(f"{elapsed:3d}s | {tasklist_count:8d} | {celery_count:6d} | {str(db_count):8s} | {status}")
            
            last_db_count = db_count
            time.sleep(5)  # Check every 5 seconds to match sync interval
            
    except KeyboardInterrupt:
        print(f"\n" + "=" * 50)
        print(f"📊 MONITORING SUMMARY")
        print(f"   • Duration: {elapsed} seconds")
        print(f"   • Syncs detected: {sync_count}")
        print(f"   • Final status: {tasklist_count} processes, {celery_count} workers, {db_count} in database")

def test_manual_operations():
    """Test manual sync operations"""
    print("\n🔧 TESTING MANUAL OPERATIONS")
    print("=" * 50)
    
    # Test silent sync
    print("1. Testing silent sync...")
    try:
        result = subprocess.run(['python', 'manage.py', 'sync_worker_status', '--silent'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✅ Silent sync completed")
        else:
            print(f"   ❌ Silent sync failed: {result.stderr}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test API response time
    print("2. Testing API response time...")
    try:
        start_time = time.time()
        response = requests.get('http://localhost:8000/queue/api/live-stats/', timeout=5)
        response_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            print(f"   ✅ API response: {response_time:.1f}ms")
        else:
            print(f"   ❌ API error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API error: {e}")

def main():
    """Main demonstration"""
    print("🎯 LIVE DATABASE SYNC EVERY 5 SECONDS")
    print("=" * 70)
    
    # Show initial status
    print("📊 INITIAL STATUS")
    print("=" * 50)
    tasklist_count, celery_count, db_count = get_current_status()
    print(f"Tasklist processes: {tasklist_count}")
    print(f"Celery workers: {celery_count}")
    print(f"Database workers: {db_count}")
    
    # Test manual operations
    test_manual_operations()
    
    print(f"\n🎯 LIVE SYNC FEATURES:")
    print("   ✅ Database sync: Every 5 seconds (automatic)")
    print("   ✅ UI updates: Every 5 seconds (real-time)")
    print("   ✅ Silent mode: Reduced logging for frequent sync")
    print("   ✅ Manual sync: python manage.py sync_worker_status --silent")
    
    print(f"\n📱 ADMIN UI TESTING:")
    print("   • Overview: http://localhost:8000/queue/admin/queue-overview/")
    print("   • Location: http://localhost:8000/queue/admin/location/744ae77f-8a7c-41d1-b9c8-00dd0fe020dd/")
    print("   • Watch for live updates every 5 seconds")
    print("   • Worker counts update automatically")
    
    print(f"\n🔄 WORKER TESTING:")
    print("   • Start worker: celery -A config worker --queues=location.744ae77f-8a7c-41d1-b9c8-00dd0fe020dd")
    print("   • Stop workers: taskkill /F /IM celery.exe")
    print("   • Watch database sync automatically")
    
    # Start monitoring
    print(f"\n📡 STARTING LIVE MONITORING")
    print("Press Ctrl+C to stop monitoring")
    print("=" * 50)
    
    monitor_live_sync()

if __name__ == '__main__':
    main()
